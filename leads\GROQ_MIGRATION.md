# Migration from Gemini to Groq LLM

This document outlines the changes made to migrate from Google's Gemini LLM to Groq's LLM service.

## Changes Made

### 1. Dependencies Updated
- Added `langchain-groq>=0.1.0` to `pyproject.toml`
- Removed dependency on `langchain-google-genai` (kept for potential fallback)

### 2. Configuration Updates
- Added `GROQ_API_KEY` to `config.py`
- Updated environment variable requirements

### 3. Qualification Service Migration
- **File**: `services/qualification_service.py`
- **Changes**:
  - Replaced `ChatGoogleGenerativeAI` with `ChatGroq`
  - Changed model from `gemini-1.5-flash` to `llama3-8b-8192`
  - Updated API key reference from `google_api_key` to `groq_api_key`
  - Changed `max_output_tokens` to `max_tokens`
  - Updated error messages to reference Groq instead of Gemini

### 4. Quota Management Updates
- **File**: `scripts/manage_quota.py`
- **Changes**:
  - Updated API key checks to use `groq_api_key`
  - Changed recommendations to reference Groq console
  - Updated safe environment template

### 5. Documentation Updates
- **File**: `scripts/README.md`
- **Changes**:
  - Updated references from Gemini to Groq
  - Changed console URL to https://console.groq.com/
  - Updated API key references

## Setup Instructions

### 1. Install Dependencies
```bash
pip install langchain-groq
```

### 2. Configure Environment Variables
Add to your `.env` file:
```bash
GROQ_API_KEY=your_groq_api_key_here
```

### 3. Get Groq API Key
1. Visit https://console.groq.com/
2. Sign up for an account
3. Generate an API key
4. Add the key to your `.env` file

### 4. Test the Integration
```bash
python test_groq_integration.py
```

## Benefits of Groq

1. **Higher Quotas**: Groq typically offers more generous free tier quotas
2. **Faster Response Times**: Groq is optimized for speed
3. **Multiple Models**: Access to various models including Llama, Mixtral, and others
4. **Cost Effective**: Generally more cost-effective than Google's Gemini

## Available Models

The system is configured to use `llama3-8b-8192` by default, but you can change to other models:

- `llama3-8b-8192` (default)
- `llama3-70b-8192`
- `mixtral-8x7b-32768`
- `gemma2-9b-it`

To change models, update the `model` parameter in `services/qualification_service.py`:

```python
self.llm = ChatGroq(
    model="mixtral-8x7b-32768",  # Change this line
    groq_api_key=settings.groq_api_key,
    temperature=0.1,
    max_tokens=2048
)
```

## Fallback Behavior

The system maintains the same fallback behavior:
- When LLM qualification is disabled, uses rule-based qualification
- When API quotas are exceeded, automatically switches to fallback mode
- Provides keyword-based scoring as a backup

## Testing

Run the test script to verify everything works:
```bash
python test_groq_integration.py
```

This will test both Groq LLM qualification and fallback qualification to ensure the system works correctly. 