"""Contact information extraction service."""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urlparse
import httpx
from config import settings

logger = logging.getLogger(__name__)


class ContactExtractionService:
    """Service for extracting email addresses and phone numbers."""
    
    def __init__(self):
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.phone_pattern = re.compile(r'(\+?1[-.]?)?\(?([0-9]{3})\)?[-.]?([0-9]{3})[-.]?([0-9]{4})')
        self.clean_phone_pattern = re.compile(r'[\d\-\(\)\s\+]+')
    
    async def extract_contacts_from_website(
        self, 
        website_url: str, 
        company_name: str
    ) -> Dict[str, Any]:
        """
        Extract email addresses and phone numbers from a website.
        
        Args:
            website_url: Company website URL
            company_name: Company name
        
        Returns:
            Dictionary with extracted contact information
        """
        try:
            # Extract from website content
            website_contacts = await self._extract_from_website_content(website_url)
            
            # Extract from contact pages
            contact_page_contacts = await self._extract_from_contact_pages(website_url)
            
            # Extract from social media profiles
            social_contacts = await self._extract_from_social_media(company_name)
            
            # Combine all results
            all_emails = set()
            all_phones = set()
            
            # Add website contacts
            if website_contacts:
                all_emails.update(website_contacts.get("emails", []))
                all_phones.update(website_contacts.get("phones", []))
            
            # Add contact page contacts
            if contact_page_contacts:
                all_emails.update(contact_page_contacts.get("emails", []))
                all_phones.update(contact_page_contacts.get("phones", []))
            
            # Add social media contacts
            if social_contacts:
                all_emails.update(social_contacts.get("emails", []))
                all_phones.update(social_contacts.get("phones", []))
            
            return {
                "emails": list(all_emails),
                "phones": list(all_phones),
                "website": website_url,
                "company_name": company_name,
                "extraction_methods": {
                    "website_content": bool(website_contacts),
                    "contact_pages": bool(contact_page_contacts),
                    "social_media": bool(social_contacts)
                }
            }
            
        except Exception as e:
            logger.error(f"Error extracting contacts from {website_url}: {e}")
            return {"emails": [], "phones": [], "website": website_url, "company_name": company_name}
    
    async def _extract_from_website_content(self, website_url: str) -> Optional[Dict[str, List[str]]]:
        """Extract contact information from main website content."""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(website_url, follow_redirects=True)
                if response.status_code == 200:
                    content = response.text
                    
                    # Extract emails
                    emails = self.email_pattern.findall(content)
                    emails = [email.lower() for email in emails if self._is_valid_email(email)]
                    
                    # Extract phone numbers
                    phones = self.phone_pattern.findall(content)
                    phones = [self._format_phone_number(phone) for phone in phones if self._is_valid_phone(phone)]
                    
                    return {
                        "emails": list(set(emails)),
                        "phones": list(set(phones))
                    }
            
        except Exception as e:
            logger.warning(f"Error extracting from website content {website_url}: {e}")
            return None
    
    async def _extract_from_contact_pages(self, website_url: str) -> Optional[Dict[str, List[str]]]:
        """Extract contact information from contact pages."""
        try:
            contact_urls = await self._find_contact_pages(website_url)
            
            all_emails = set()
            all_phones = set()
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                for contact_url in contact_urls:
                    try:
                        response = await client.get(contact_url, follow_redirects=True)
                        if response.status_code == 200:
                            content = response.text
                            
                            # Extract emails
                            emails = self.email_pattern.findall(content)
                            emails = [email.lower() for email in emails if self._is_valid_email(email)]
                            all_emails.update(emails)
                            
                            # Extract phone numbers
                            phones = self.phone_pattern.findall(content)
                            phones = [self._format_phone_number(phone) for phone in phones if self._is_valid_phone(phone)]
                            all_phones.update(phones)
                    
                    except Exception as e:
                        logger.warning(f"Error extracting from contact page {contact_url}: {e}")
                        continue
            
            return {
                "emails": list(all_emails),
                "phones": list(all_phones)
            }
            
        except Exception as e:
            logger.warning(f"Error extracting from contact pages {website_url}: {e}")
            return None
    
    async def _find_contact_pages(self, website_url: str) -> List[str]:
        """Find contact page URLs."""
        contact_paths = [
            "/contact", "/contact-us", "/about/contact", "/get-in-touch",
            "/reach-us", "/connect", "/support", "/help"
        ]
        
        contact_urls = []
        base_url = self._get_base_url(website_url)
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            for path in contact_paths:
                contact_url = f"{base_url}{path}"
                try:
                    response = await client.head(contact_url)
                    if response.status_code == 200:
                        contact_urls.append(contact_url)
                except:
                    continue
        
        return contact_urls
    
    async def _extract_from_social_media(self, company_name: str) -> Optional[Dict[str, List[str]]]:
        """Extract contact information from social media profiles."""
        # This would require social media API access
        # For now, return empty results
        return None
    
    def _is_valid_email(self, email: str) -> bool:
        """Validate email address."""
        if not email or len(email) < 5:
            return False
        
        # Check for common invalid patterns
        invalid_patterns = [
            'example.com', 'test.com', 'domain.com', 'your-email',
            'noreply', 'no-reply', 'donotreply', 'do-not-reply'
        ]
        
        email_lower = email.lower()
        for pattern in invalid_patterns:
            if pattern in email_lower:
                return False
        
        return True
    
    def _is_valid_phone(self, phone_tuple: Tuple[str, ...]) -> bool:
        """Validate phone number."""
        if not phone_tuple or len(phone_tuple) < 3:
            return False
        
        # Check if it's a valid US phone number
        area_code = phone_tuple[1] if len(phone_tuple) > 1 else ""
        if area_code and len(area_code) == 3:
            return True
        
        return False
    
    def _format_phone_number(self, phone_tuple: Tuple[str, ...]) -> str:
        """Format phone number consistently."""
        if len(phone_tuple) >= 4:
            country_code = phone_tuple[0] if phone_tuple[0] else ""
            area_code = phone_tuple[1]
            prefix = phone_tuple[2]
            line_number = phone_tuple[3]
            
            if country_code and country_code != "+1":
                return f"{country_code} ({area_code}) {prefix}-{line_number}"
            else:
                return f"({area_code}) {prefix}-{line_number}"
        
        return "".join(phone_tuple)
    
    def _get_base_url(self, url: str) -> str:
        """Get base URL from full URL."""
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"
    
    async def extract_contacts_from_text(self, text: str) -> Dict[str, List[str]]:
        """Extract contact information from text content."""
        try:
            # Extract emails
            emails = self.email_pattern.findall(text)
            emails = [email.lower() for email in emails if self._is_valid_email(email)]
            
            # Extract phone numbers
            phones = self.phone_pattern.findall(text)
            phones = [self._format_phone_number(phone) for phone in phones if self._is_valid_phone(phone)]
            
            return {
                "emails": list(set(emails)),
                "phones": list(set(phones))
            }
            
        except Exception as e:
            logger.error(f"Error extracting contacts from text: {e}")
            return {"emails": [], "phones": []}
    
    async def extract_contacts_from_multiple_sources(
        self, 
        companies: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Extract contact information from multiple companies."""
        results = []
        
        for company in companies:
            website_url = company.get("website")
            company_name = company.get("company_name", "")
            
            if website_url:
                contacts = await self.extract_contacts_from_website(website_url, company_name)
                results.append({
                    **company,
                    "email": contacts.get("emails", [])[0] if contacts.get("emails") else None,
                    "phone": contacts.get("phones", [])[0] if contacts.get("phones") else None,
                    "all_emails": contacts.get("emails", []),
                    "all_phones": contacts.get("phones", []),
                    "contact_extraction_metadata": contacts.get("extraction_methods", {})
                })
            else:
                # If no website, try to extract from description
                description = company.get("description", "")
                if description:
                    contacts = await self.extract_contacts_from_text(description)
                    results.append({
                        **company,
                        "email": contacts.get("emails", [])[0] if contacts.get("emails") else None,
                        "phone": contacts.get("phones", [])[0] if contacts.get("phones") else None,
                        "all_emails": contacts.get("emails", []),
                        "all_phones": contacts.get("phones", []),
                        "contact_extraction_metadata": {"text_content": True}
                    })
                else:
                    results.append({
                        **company,
                        "email": None,
                        "phone": None,
                        "all_emails": [],
                        "all_phones": [],
                        "contact_extraction_metadata": {}
                    })
        
        return results 