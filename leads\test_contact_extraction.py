#!/usr/bin/env python3
"""
Test script for contact information extraction.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.contact_extraction_service import ContactExtractionService
from services.search_service import SearchService


async def test_contact_extraction():
    """Test contact information extraction from various sources."""
    
    print("🧪 Testing Contact Information Extraction...")
    print("=" * 60)
    
    contact_service = ContactExtractionService()
    
    # Test 1: Extract from text content
    print("\n📝 Test 1: Extract from text content")
    test_text = """
    Contact <NAME_EMAIL> or call us at (*************.
    For sales inquiries, email <EMAIL> or call (*************.
    Our office is located at 123 Main Street, City, State 12345.
    """
    
    contacts = await contact_service.extract_contacts_from_text(test_text)
    print(f"✅ Emails found: {contacts.get('emails', [])}")
    print(f"✅ Phones found: {contacts.get('phones', [])}")
    
    # Test 2: Extract from website (if available)
    print("\n🌐 Test 2: Extract from website")
    test_website = "https://example.com"
    website_contacts = await contact_service.extract_contacts_from_website(
        test_website, "Test Company"
    )
    print(f"✅ Website emails: {website_contacts.get('emails', [])}")
    print(f"✅ Website phones: {website_contacts.get('phones', [])}")
    
    # Test 3: Extract from multiple sources
    print("\n📊 Test 3: Extract from multiple sources")
    test_companies = [
        {
            "company_name": "Test Company 1",
            "website": "https://example.com",
            "description": "Contact <NAME_EMAIL> or (*************"
        },
        {
            "company_name": "Test Company 2",
            "description": "Email <NAME_EMAIL> or call (*************"
        },
        {
            "company_name": "Test Company 3",
            "description": "No contact information available"
        }
    ]
    
    results = await contact_service.extract_contacts_from_multiple_sources(test_companies)
    
    for i, result in enumerate(results, 1):
        print(f"  Company {i}: {result['company_name']}")
        print(f"    Email: {result.get('email', 'None')}")
        print(f"    Phone: {result.get('phone', 'None')}")
        print(f"    All emails: {result.get('all_emails', [])}")
        print(f"    All phones: {result.get('all_phones', [])}")
    
    return True


async def test_search_with_contacts():
    """Test search service with contact information focus."""
    
    print("\n🔍 Test 4: Search with contact focus")
    print("=" * 60)
    
    search_service = SearchService()
    
    # Test search for forex companies with contact information
    search_results = await search_service.search_leads(
        query="funded trading programs",
        industry="forex",
        max_results=5
    )
    
    print(f"✅ Found {len(search_results)} search results")
    
    # Show results with contact information
    for i, result in enumerate(search_results, 1):
        print(f"\n  Result {i}: {result.get('company_name', 'Unknown')}")
        print(f"    Website: {result.get('website', 'None')}")
        print(f"    Email: {result.get('email', 'None')}")
        print(f"    Phone: {result.get('phone', 'None')}")
        print(f"    Has contact info: {result.get('has_contact_info', False)}")
    
    return True


async def main():
    """Main test function."""
    
    print("🚀 Starting Contact Extraction Tests...")
    print("=" * 60)
    
    # Test contact extraction
    extraction_success = await test_contact_extraction()
    
    # Test search with contacts
    search_success = await test_search_with_contacts()
    
    print("\n" + "=" * 60)
    print("📋 Test Results:")
    print(f"  • Contact Extraction: {'✅ PASS' if extraction_success else '❌ FAIL'}")
    print(f"  • Search with Contacts: {'✅ PASS' if search_success else '❌ FAIL'}")
    
    if extraction_success and search_success:
        print("\n🎉 All tests passed! Contact extraction is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Please check the configuration.")
    
    return extraction_success and search_success


if __name__ == "__main__":
    asyncio.run(main()) 