"""Search service using <PERSON><PERSON> for lead discovery."""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from tavily import <PERSON>ly<PERSON><PERSON>
from config import settings

logger = logging.getLogger(__name__)


class SearchService:
    """Service for searching potential leads using <PERSON><PERSON>."""
    
    def __init__(self):
        self.client = TavilyClient(api_key=settings.tavily_api_key)
    
    async def search_leads(
        self,
        query: str,
        industry: str,
        max_results: int = 50,
        search_depth: str = "advanced"
    ) -> List[Dict[str, Any]]:
        """
        Search for potential leads using Tavily.
        
        Args:
            query: Search query
            industry: Target industry
            max_results: Maximum number of results
            search_depth: Search depth (basic, advanced)
        
        Returns:
            List of search results with company information
        """
        try:
            # Construct industry-specific search queries
            search_queries = self._build_search_queries(query, industry)
            
            all_results = []
            
            for search_query in search_queries:
                logger.info(f"Searching for: {search_query}")

                # Calculate results per query (minimum 1, maximum 10)
                results_per_query = max(1, min(10, max_results // len(search_queries)))

                # Perform search
                response = await self._perform_search(
                    search_query, results_per_query, search_depth
                )
                
                # Process and filter results
                processed_results = self._process_search_results(response, industry)
                all_results.extend(processed_results)
                
                # Add delay to respect rate limits
                await asyncio.sleep(settings.search_delay_seconds)
            
            # Remove duplicates and limit results
            unique_results = self._deduplicate_results(all_results)
            return unique_results[:max_results]
            
        except Exception as e:
            logger.error(f"Error in search_leads: {e}")
            return []
    
    def _build_search_queries(self, base_query: str, industry: str) -> List[str]:
        """Build industry-specific search queries focused on finding actual companies."""
        queries = []

        # For forex industry, focus on finding actual companies, not social media posts
        if industry == "forex" and "funded trading" in base_query.lower():
            # Search for actual prop firms and forex companies
            queries.extend([
                "FTMO prop trading firm contact information",
                "MyForexFunds proprietary trading contact",
                "Apex Trader Funding company contact",
                "Topstep funded trading contact information",
                "FundedNext prop firm contact details",
                "The5ers trading company contact",
                "SurgeTrader proprietary trading contact",
                "City Traders Imperium contact information",
                "Earn2Trade funded account contact",
                "forex prop trading firms directory",
                "proprietary trading companies contact list",
                "funded trading programs companies",
                "forex broker companies contact information",
                "trading technology companies email phone"
            ])
        else:
            # Base query with contact focus
            queries.append(f"{base_query} {industry} contact email phone")
        
        # Industry-specific variations with contact focus
        if industry == "healthcare":
            queries.extend([
                f"{base_query} healthcare software companies contact information",
                f"{base_query} medical technology companies email phone",
                f"{base_query} healthcare IT solutions contact details",
                f"{base_query} digital health companies contact us"
            ])
        elif industry == "finance":
            queries.extend([
                f"{base_query} fintech companies contact information",
                f"{base_query} financial software companies email phone",
                f"{base_query} banking technology companies contact details",
                f"{base_query} financial services software contact us"
            ])
        elif industry == "e-commerce":
            queries.extend([
                f"{base_query} ecommerce software companies contact information",
                f"{base_query} online retail technology email phone",
                f"{base_query} ecommerce platform companies contact details",
                f"{base_query} digital commerce solutions contact us"
            ])
        elif industry == "forex":
            # Focus on actual forex companies and prop firms, not social media posts
            queries.extend([
                "forex prop trading firms contact information",
                "funded trading programs companies email phone",
                "proprietary trading firms contact details",
                "forex broker companies contact information",
                "trading technology companies contact email",
                "forex software companies contact us",
                "prop firm evaluation companies contact",
                "forex trading platform companies email",
                "currency trading companies contact details",
                "forex technology providers contact information"
            ])
        elif industry == "software":
            queries.extend([
                f"{base_query} software companies contact information",
                f"{base_query} technology companies email phone",
                f"{base_query} software development companies contact details",
                f"{base_query} tech solutions contact us"
            ])
        else:
            queries.extend([
                f"{base_query} {industry} companies contact information",
                f"{base_query} {industry} businesses email phone",
                f"{base_query} {industry} services contact details",
                f"{base_query} {industry} solutions contact us"
            ])
        
        return queries
    
    async def _perform_search(
        self, query: str, max_results: int, search_depth: str
    ) -> Dict[str, Any]:
        """Perform search using Tavily API."""
        try:
            response = self.client.search(
                query=query,
                search_depth=search_depth,
                max_results=max_results,
                include_answer=False,
                include_raw_content=False,
                include_images=False
            )
            return response
        except Exception as e:
            logger.error(f"Tavily search error: {e}")
            return {"results": []}
    
    def _process_search_results(
        self, response: Dict[str, Any], industry: str
    ) -> List[Dict[str, Any]]:
        """Process and filter search results."""
        processed_results = []
        
        for result in response.get("results", []):
            try:
                # Extract company information
                company_info = self._extract_company_info(result, industry)
                if company_info:
                    processed_results.append(company_info)
            except Exception as e:
                logger.warning(f"Error processing search result: {e}")
                continue
        
        return processed_results
    
    def _extract_company_info(
        self, result: Dict[str, Any], industry: str
    ) -> Optional[Dict[str, Any]]:
        """Extract company information from search result with focus on contact info."""
        try:
            title = result.get("title", "")
            content = result.get("content", "")
            url = result.get("url", "")
            
            # Basic validation
            if not title or not content:
                return None
            
            # Extract company name from title
            company_name = self._extract_company_name(title, content)
            if not company_name:
                return None
            
            # Extract website
            website = self._extract_website(url, content)
            
            # Extract contact information from content
            email, phone = self._extract_contact_info(content)
            
            # Extract location
            location = self._extract_location(content)
            
            # Extract services/description
            services = self._extract_services(content, industry)
            
            return {
                "company_name": company_name,
                "website": website,
                "email": email,
                "phone": phone,
                "location": location,
                "services": services,
                "description": content[:500] if content else "",
                "source_url": url,
                "industry": industry,
                "raw_data": result,
                "has_contact_info": bool(email or phone)
            }
            
        except Exception as e:
            logger.warning(f"Error extracting company info: {e}")
            return None
    
    def _extract_company_name(self, title: str, content: str) -> Optional[str]:
        """Extract company name from title and content with focus on forex companies."""
        import re

        # Known forex company patterns
        forex_companies = [
            'FTMO', 'MyForexFunds', 'Apex Trader Funding', 'Topstep', 'FundedNext',
            'The5ers', 'SurgeTrader', 'City Traders Imperium', 'Earn2Trade',
            'Forex.com', 'OANDA', 'IG Group', 'Plus500', 'eToro', 'XM Group'
        ]

        # Check if any known forex company is mentioned
        for company in forex_companies:
            if company.lower() in title.lower() or company.lower() in content.lower():
                return company

        # Remove common suffixes and prefixes from title
        title_clean = re.sub(r'\s*-\s*.*$', '', title)
        title_clean = re.sub(r'\s*\|.*$', '', title_clean)
        title_clean = re.sub(r'\s*:.*$', '', title_clean)
        title_clean = re.sub(r'^\s*(About|Contact|Home)\s*-?\s*', '', title_clean, flags=re.IGNORECASE)

        # Extract potential company names
        potential_names = []

        # From title
        if len(title_clean) > 3 and len(title_clean) < 100:
            potential_names.append(title_clean.strip())
        
        # From content (first few sentences)
        sentences = content.split('.')[:3]
        for sentence in sentences:
            # Look for capitalized words that might be company names
            words = sentence.split()
            for i, word in enumerate(words):
                if (word[0].isupper() and len(word) > 2 and 
                    not word.lower() in ['the', 'and', 'for', 'with', 'from', 'about']):
                    # Try to get company name (1-3 words)
                    company_candidate = ' '.join(words[i:i+3])
                    if len(company_candidate) > 3 and len(company_candidate) < 50:
                        potential_names.append(company_candidate)
        
        # Return the most likely company name
        if potential_names:
            return potential_names[0]
        
        return None
    
    def _extract_website(self, url: str, content: str) -> Optional[str]:
        """Extract website URL."""
        import re
        
        # Try to extract from URL
        if url and ('http://' in url or 'https://' in url):
            return url
        
        # Try to extract from content
        url_pattern = r'https?://[^\s]+'
        urls = re.findall(url_pattern, content)
        if urls:
            return urls[0]
        
        return None
    
    def _extract_location(self, content: str) -> Optional[str]:
        """Extract location from content."""
        # This is a simplified location extraction
        # In a real implementation, you might use NER or more sophisticated parsing
        import re
        
        # Common location patterns
        location_patterns = [
            r'\b[A-Z][a-z]+,\s*[A-Z]{2}\b',  # City, State
            r'\b[A-Z][a-z]+,\s*[A-Z][a-z]+\b',  # City, Country
        ]
        
        for pattern in location_patterns:
            matches = re.findall(pattern, content)
            if matches:
                return matches[0]
        
        return None
    
    def _extract_services(self, content: str, industry: str) -> Optional[str]:
        """Extract services from content."""
        # Industry-specific service keywords
        service_keywords = {
            "healthcare": ["healthcare", "medical", "clinical", "patient", "hospital"],
            "finance": ["financial", "banking", "trading", "investment", "fintech"],
            "e-commerce": ["ecommerce", "online", "retail", "shopping", "digital"],
            "forex": ["forex", "trading", "currency", "exchange", "financial"],
            "software": ["software", "development", "technology", "digital", "solutions"]
        }
        
        keywords = service_keywords.get(industry, [])
        content_lower = content.lower()
        
        found_services = []
        for keyword in keywords:
            if keyword in content_lower:
                found_services.append(keyword)
        
        if found_services:
            return ", ".join(found_services)
        
        return None
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate results based on company name and website."""
        seen_companies = set()
        unique_results = []
        
        for result in results:
            company_key = (
                result.get("company_name", "").lower(),
                result.get("website", "").lower()
            )
            
            if company_key not in seen_companies and company_key[0]:
                seen_companies.add(company_key)
                unique_results.append(result)
        
        return unique_results 

    def _extract_contact_info(self, content: str) -> tuple[Optional[str], Optional[str]]:
        """Extract email and phone from content with improved patterns."""
        import re

        # Enhanced email pattern
        email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', re.IGNORECASE)
        emails = email_pattern.findall(content)

        # Enhanced phone patterns (multiple formats)
        phone_patterns = [
            re.compile(r'(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'),  # US format
            re.compile(r'\+?[0-9]{1,4}[-.\s]?[0-9]{1,4}[-.\s]?[0-9]{1,4}[-.\s]?[0-9]{1,4}'),  # International
            re.compile(r'\b[0-9]{3}[-.\s][0-9]{3}[-.\s][0-9]{4}\b'),  # Simple format
        ]

        phones = []
        for pattern in phone_patterns:
            phones.extend(pattern.findall(content))

        # Get first valid email
        email = None
        for email_candidate in emails:
            email_lower = email_candidate.lower()
            # Filter out common invalid patterns
            invalid_patterns = ['example.com', 'test.com', 'domain.com', 'your-email', 'noreply', 'no-reply']
            if not any(pattern in email_lower for pattern in invalid_patterns):
                email = email_candidate.lower()
                break
        
        # Get first valid phone
        phone = None
        if phones:
            phone_tuple = phones[0]
            if len(phone_tuple) >= 4:
                area_code = phone_tuple[1]
                prefix = phone_tuple[2]
                line_number = phone_tuple[3]
                phone = f"({area_code}) {prefix}-{line_number}"
        
        return email, phone 