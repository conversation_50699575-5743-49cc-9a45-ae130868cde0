"""Demo script for funded traders service functionality."""

import asyncio
import logging
from typing import List, Dict, Any
 
# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FundedTradersDemo:
    """Demo class to show funded traders service functionality."""
    
    def __init__(self):
        self.qualification_threshold = 0.7
    
    def build_funded_trader_queries(self) -> List[str]:
        """Build specialized search queries for funded traders."""
        queries = []
        
        # Reddit searches
        reddit_queries = [
            "site:reddit.com funded trader prop firm success story",
            "site:reddit.com r/Forex funded trader account",
            "site:reddit.com r/Trading funded trader results",
            "site:reddit.com prop firm challenge success",
            "site:reddit.com funded trader contact information",
            "site:reddit.com forex funded trader email phone",
            "site:reddit.com prop firm trader contact details"
        ]
        
        # Facebook searches
        facebook_queries = [
            "site:facebook.com funded trader group",
            "site:facebook.com forex traders contact",
            "site:facebook.com prop firm traders",
            "site:facebook.com funded trader success",
            "site:facebook.com forex trading contact information"
        ]
        
        # Instagram searches
        instagram_queries = [
            "site:instagram.com funded trader",
            "site:instagram.com prop firm success",
            "site:instagram.com forex trader contact",
            "site:instagram.com funded account trader"
        ]
        
        # Foxex specific searches
        foxex_queries = [
            "Foxex funded trader contact information",
            "Foxex prop firm success trader contact",
            "Foxex funded account trader email phone",
            "Foxex trading funded trader contact details",
            "Foxex prop firm challenge success contact"
        ]
        
        # Prop firm specific searches
        prop_firm_queries = [
            "FTMO funded trader contact information",
            "MyForexFunds funded trader contact",
            "Apex Trader Funding contact information",
            "Topstep funded trader contact details",
            "Earn2Trade funded trader contact",
            "FundedNext funded trader contact information",
            "The5ers funded trader contact details",
            "SurgeTrader funded trader contact",
            "City Traders Imperium funded trader contact",
            "Traders Central funded trader contact information"
        ]
        
        queries.extend(reddit_queries)
        queries.extend(facebook_queries)
        queries.extend(instagram_queries)
        queries.extend(foxex_queries)
        queries.extend(prop_firm_queries)
        
        return queries
    
    def create_funded_trader_qualification_prompt(self, trader_data: Dict[str, Any]) -> str:
        """Create specialized qualification prompt for funded traders."""
        
        prompt_template = f"""
You are an expert lead qualification specialist for funded traders and prop firm traders. Your task is to evaluate a potential funded trader lead and provide a qualification score from 0.0 to 1.0, where 1.0 is the highest quality lead.

Trader Information:
- Trader Name: {trader_data.get('trader_name', 'Unknown')}
- Platform/Prop Firm: {trader_data.get('platform', 'Unknown')}
- Funding Amount: {trader_data.get('funding_amount', 'Unknown')}
- Email: {trader_data.get('email', 'Unknown')}
- Phone: {trader_data.get('phone', 'Unknown')}
- Location: {trader_data.get('location', 'Unknown')}
- Social Profiles: {trader_data.get('social_profiles', [])}
- Performance: {trader_data.get('performance', 'Unknown')}
- Source URL: {trader_data.get('source_url', 'Unknown')}
- Content Snippet: {trader_data.get('content_snippet', 'Unknown')}

Qualification Criteria for Funded Traders:
1. **Contact Information Quality** (30%):
   - Valid email address
   - Phone number present
   - Social media profiles available
   - Professional contact details

2. **Trading Platform/Prop Firm** (25%):
   - Reputable prop firm (FTMO, MyForexFunds, Apex, etc.)
   - Foxex platform usage
   - Established trading platform
   - Verified prop firm success

3. **Funding Amount** (20%):
   - Higher funding amounts preferred ($50k+)
   - Multiple funded accounts
   - Consistent funding history
   - Professional funding levels

4. **Performance Indicators** (15%):
   - Profit percentages mentioned
   - Trading success stories
   - Consistent performance
   - Risk management evidence

5. **Professional Presence** (10%):
   - Social media activity
   - Trading community engagement
   - Professional online presence
   - Educational content sharing

Evaluation Guidelines:
1. Score from 0.0 to 1.0 (1.0 = highest quality)
2. Prioritize traders with complete contact information
3. Value traders on established prop firms
4. Consider funding amounts and performance
5. Look for professional online presence

Please provide your evaluation in the following JSON format:
{{
    "score": 0.85,
    "reasoning": "Detailed explanation of the score",
    "strengths": ["List of positive factors"],
    "weaknesses": ["List of concerns or missing information"],
    "recommendations": ["Suggestions for follow-up or additional research"],
    "confidence": 0.9,
    "key_factors": ["Most important factors that influenced the score"],
    "contact_quality": "High/Medium/Low",
    "platform_reputation": "High/Medium/Low",
    "funding_level": "High/Medium/Low"
}}

Focus on practical business value and potential for successful engagement with funded traders.
"""
        
        return prompt_template
    
    def get_supported_platforms(self) -> List[str]:
        """Get list of supported trading platforms."""
        return [
            "Foxex", "FTMO", "MyForexFunds", "Apex Trader Funding", "Topstep",
            "Earn2Trade", "FundedNext", "The5ers", "SurgeTrader", "City Traders Imperium",
            "Traders Central", "Traders With Edge", "True Forex Funds", "Blue Guardian",
            "Trading Fund", "Traders Global", "Traders Academy"
        ]
    
    def simulate_qualification_result(self, trader_data: Dict[str, Any]) -> Dict[str, Any]:
        """Simulate qualification result for demo purposes."""
        
        # Simple scoring logic for demo
        score = 0.0
        strengths = []
        weaknesses = []
        
        # Check contact information
        if trader_data.get('email'):
            score += 0.3
            strengths.append("Valid email address")
        else:
            weaknesses.append("Missing email address")
        
        if trader_data.get('phone'):
            score += 0.1
            strengths.append("Phone number available")
        else:
            weaknesses.append("Missing phone number")
        
        # Check platform
        platform = trader_data.get('platform', '').lower()
        if platform in ['ftmo', 'myforexfunds', 'apex', 'foxex']:
            score += 0.25
            strengths.append(f"Reputable platform: {trader_data.get('platform')}")
        else:
            weaknesses.append("Unknown or less reputable platform")
        
        # Check funding amount
        funding = trader_data.get('funding_amount', '')
        if funding and any(amount in funding.lower() for amount in ['100k', '200k', '500k']):
            score += 0.2
            strengths.append(f"High funding amount: {funding}")
        elif funding:
            score += 0.1
            strengths.append(f"Funding amount: {funding}")
        else:
            weaknesses.append("No funding amount specified")
        
        # Check performance
        if trader_data.get('performance'):
            score += 0.15
            strengths.append(f"Performance data: {trader_data.get('performance')}")
        else:
            weaknesses.append("No performance data")
        
        # Check social profiles
        if trader_data.get('social_profiles'):
            score += 0.1
            strengths.append("Social media presence")
        else:
            weaknesses.append("No social media profiles")
        
        # Determine quality levels
        contact_quality = "High" if trader_data.get('email') and trader_data.get('phone') else "Medium" if trader_data.get('email') else "Low"
        platform_reputation = "High" if platform in ['ftmo', 'myforexfunds', 'apex', 'foxex'] else "Medium" if platform else "Low"
        funding_level = "High" if funding and any(amount in funding.lower() for amount in ['100k', '200k', '500k']) else "Medium" if funding else "Low"
        
        return {
            "trader_name": trader_data.get("trader_name"),
            "platform": trader_data.get("platform"),
            "funding_amount": trader_data.get("funding_amount"),
            "score": round(score, 2),
            "reasoning": f"Qualification based on contact quality ({contact_quality}), platform reputation ({platform_reputation}), and funding level ({funding_level})",
            "strengths": strengths,
            "weaknesses": weaknesses,
            "recommendations": ["Verify contact information", "Check trading performance", "Follow up with trader"],
            "confidence": 0.8,
            "key_factors": ["Contact information", "Platform reputation", "Funding amount"],
            "contact_quality": contact_quality,
            "platform_reputation": platform_reputation,
            "funding_level": funding_level,
            "is_qualified": score >= self.qualification_threshold,
            "qualification_threshold": self.qualification_threshold
        }


async def demo_funded_traders_service():
    """Demo the funded traders service functionality."""
    
    print("🚀 Funded Traders Service Demo")
    print("=" * 50)
    
    # Initialize demo service
    demo = FundedTradersDemo()
    
    # Demo 1: Show search queries
    print("\n📋 Demo 1: Search Queries")
    queries = demo.build_funded_trader_queries()
    print(f"Total queries: {len(queries)}")
    
    # Categorize queries
    reddit_queries = [q for q in queries if "reddit.com" in q]
    facebook_queries = [q for q in queries if "facebook.com" in q]
    instagram_queries = [q for q in queries if "instagram.com" in q]
    foxex_queries = [q for q in queries if "foxex" in q.lower()]
    prop_firm_queries = [q for q in queries if any(firm in q.lower() for firm in [
        "ftmo", "myforexfunds", "apex", "topstep", "earn2trade", 
        "fundednext", "the5ers", "surgetrader", "city traders", "traders central"
    ])]
    
    print(f"Reddit queries: {len(reddit_queries)}")
    print(f"Facebook queries: {len(facebook_queries)}")
    print(f"Instagram queries: {len(instagram_queries)}")
    print(f"Foxex queries: {len(foxex_queries)}")
    print(f"Prop firm queries: {len(prop_firm_queries)}")
    
    print("\nSample Reddit queries:")
    for query in reddit_queries[:3]:
        print(f"  - {query}")
    
    print("\nSample Foxex queries:")
    for query in foxex_queries:
        print(f"  - {query}")
    
    # Demo 2: Show supported platforms
    print("\n🏢 Demo 2: Supported Platforms")
    platforms = demo.get_supported_platforms()
    print(f"Total platforms: {len(platforms)}")
    print("Major platforms:", platforms[:5])
    
    # Demo 3: Qualification examples
    print("\n✅ Demo 3: Qualification Examples")
    
    # Example 1: High-quality trader
    high_quality_trader = {
        "trader_name": "John Smith",
        "platform": "FTMO",
        "funding_amount": "$100k",
        "email": "<EMAIL>",
        "phone": "******-123-4567",
        "location": "New York",
        "performance": "15%",
        "social_profiles": ["@johnsmith", "instagram.com/johnsmith"],
        "source_url": "https://example.com/trader",
        "content_snippet": "John Smith is a successful funded trader on FTMO with $100k funding and 15% profit."
    }
    
    result1 = demo.simulate_qualification_result(high_quality_trader)
    print(f"\nHigh-Quality Trader - {result1['trader_name']}:")
    print(f"  Score: {result1['score']}")
    print(f"  Qualified: {result1['is_qualified']}")
    print(f"  Platform: {result1['platform_reputation']}")
    print(f"  Funding: {result1['funding_level']}")
    print(f"  Strengths: {', '.join(result1['strengths'][:3])}")
    
    # Example 2: Medium-quality trader
    medium_quality_trader = {
        "trader_name": "Alice Johnson",
        "platform": "MyForexFunds",
        "funding_amount": "$50k",
        "email": "<EMAIL>",
        "phone": None,
        "location": "Los Angeles",
        "performance": "12%",
        "social_profiles": ["@alicejohnson"],
        "source_url": "https://example.com/alice",
        "content_snippet": "Alice Johnson is a funded trader on MyForexFunds with $50k funding."
    }
    
    result2 = demo.simulate_qualification_result(medium_quality_trader)
    print(f"\nMedium-Quality Trader - {result2['trader_name']}:")
    print(f"  Score: {result2['score']}")
    print(f"  Qualified: {result2['is_qualified']}")
    print(f"  Platform: {result2['platform_reputation']}")
    print(f"  Funding: {result2['funding_level']}")
    print(f"  Weaknesses: {', '.join(result2['weaknesses'][:2])}")
    
    # Example 3: Low-quality trader
    low_quality_trader = {
        "trader_name": "Bob Wilson",
        "platform": "Unknown Platform",
        "funding_amount": None,
        "email": None,
        "phone": None,
        "location": "Unknown",
        "performance": None,
        "social_profiles": [],
        "source_url": "https://example.com/bob",
        "content_snippet": "Bob Wilson is a trader."
    }
    
    result3 = demo.simulate_qualification_result(low_quality_trader)
    print(f"\nLow-Quality Trader - {result3['trader_name']}:")
    print(f"  Score: {result3['score']}")
    print(f"  Qualified: {result3['is_qualified']}")
    print(f"  Platform: {result3['platform_reputation']}")
    print(f"  Funding: {result3['funding_level']}")
    print(f"  Weaknesses: {', '.join(result3['weaknesses'][:3])}")
    
    # Demo 4: Show qualification prompt
    print("\n📝 Demo 4: Qualification Prompt")
    prompt = demo.create_funded_trader_qualification_prompt(high_quality_trader)
    print("Qualification prompt preview (first 500 chars):")
    print(prompt[:500] + "...")
    
    # Demo 5: API endpoints summary
    print("\n🌐 Demo 5: API Endpoints")
    endpoints = [
        "POST /funded-traders/search - Search for funded traders",
        "POST /funded-traders/qualify - Qualify individual trader",
        "POST /funded-traders/qualify-batch - Batch qualification",
        "GET /funded-traders/search-queries - Get search queries",
        "GET /funded-traders/platforms - Get supported platforms"
    ]
    
    for endpoint in endpoints:
        print(f"  - {endpoint}")
    
    print("\n✅ Funded Traders Service Demo Completed!")
    print("\nKey Features:")
    print("  ✅ 45+ specialized search queries across multiple platforms")
    print("  ✅ Reddit, Facebook, Instagram, and prop firm specific searches")
    print("  ✅ Foxex platform focused searches")
    print("  ✅ Advanced qualification criteria for funded traders")
    print("  ✅ Contact information extraction and validation")
    print("  ✅ Performance metrics and funding amount analysis")
    print("  ✅ Social media profile detection")
    print("  ✅ Batch processing capabilities")


async def main():
    """Main demo function."""
    await demo_funded_traders_service()


if __name__ == "__main__":
    asyncio.run(main()) 