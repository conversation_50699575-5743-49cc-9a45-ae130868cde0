# AI Lead Generation Agent Requirements
# Core AI and Workflow
langgraph>=0.3.0
langchain>=0.3.0
langchain-google-genai>=2.0.0

# Web Framework
fastapi>=0.110.0
uvicorn>=0.27.0

# Data Validation and Settings
pydantic>=2.6.0
pydantic-settings>=2.2.0

# Search and Scraping
tavily-python>=0.3.0
scrapegraphai>=0.1.0
beautifulsoup4>=4.12.0

# Database
asyncpg>=0.29.0
sqlalchemy>=2.0.25
alembic>=1.13.0

# HTTP Client
httpx>=0.27.0

# File Handling
aiofiles>=23.2.0
python-multipart>=0.0.9

# Environment Management
python-dotenv>=1.0.0