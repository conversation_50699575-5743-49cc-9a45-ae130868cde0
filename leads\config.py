"""Configuration settings for the Lead Generation AI Agent."""

import os
from typing import List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # API Keys
    google_api_key: str = Field(..., env="GOOGLE_API_KEY")
    groq_api_key: str = Field(..., env="GROQ_API_KEY")
    tavily_api_key: str = Field(..., env="TAVILY_API_KEY")
    scrapegraph_api_key: str = Field(..., env="SCRAPEGRAPH_API_KEY")
    
    # Database Configuration
    database_url: str = Field(..., env="DATABASE_URL")
    neon_database_url: str = Field(..., env="NEON_DATABASE_URL")
    
    # Application Settings
    app_name: str = Field(default="Lead Generation AI Agent", env="APP_NAME")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Lead Generation Settings
    max_leads_per_search: int = Field(default=50, env="MAX_LEADS_PER_SEARCH")
    lead_qualification_threshold: float = Field(default=0.7, env="LEAD_QUALIFICATION_THRESHOLD")
    search_delay_seconds: int = Field(default=2, env="SEARCH_DELAY_SECONDS")
    
    # LLM Settings
    enable_llm_qualification: bool = Field(default=True, env="ENABLE_LLM_QUALIFICATION")
    llm_concurrency_limit: int = Field(default=5, env="LLM_CONCURRENCY_LIMIT")
    use_fallback_qualification: bool = Field(default=True, env="USE_FALLBACK_QUALIFICATION")
    
    # Target Industries
    target_industries: List[str] = Field(
        default=["healthcare", "finance", "e-commerce", "forex", "software"],
        env="TARGET_INDUSTRIES"
    )
    
    # Compliance Settings
    gdpr_compliant: bool = Field(default=True, env="GDPR_COMPLIANT")
    data_retention_days: int = Field(default=90, env="DATA_RETENTION_DAYS")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings() 