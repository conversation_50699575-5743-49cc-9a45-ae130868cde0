#!/usr/bin/env python3
"""
Script to manage API quota and provide options for handling quota limits.
"""

import os
import sys
import argparse
from datetime import datetime, timedelta
from typing import Dict, Any

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings


def check_quota_status() -> Dict[str, Any]:
    """Check current quota status and provide recommendations."""
    
    print("🔍 Checking API Quota Status...")
    print("=" * 50)
    
    # Check if API keys are configured
    if not settings.groq_api_key:
        print("❌ Groq API key not configured")
        return {"status": "error", "message": "Groq API key not configured"}
    
    print("✅ Groq API key configured")
    
    # Provide quota management recommendations
    recommendations = {
        "immediate_actions": [
            "Set ENABLE_LLM_QUALIFICATION=false in your .env file to disable LLM qualification temporarily",
            "Set USE_FALLBACK_QUALIFICATION=true to use rule-based qualification",
            "Reduce LLM_CONCURRENCY_LIMIT to 1-2 to minimize API calls"
        ],
        "long_term_solutions": [
            "Upgrade to a paid Groq plan for higher quotas",
            "Implement caching to reduce redundant API calls",
            "Use multiple API keys and rotate them",
            "Implement rate limiting with exponential backoff"
        ],
        "alternative_approaches": [
            "Use local LLM models (e.g., Ollama) for qualification",
            "Implement rule-based qualification as primary method",
            "Use different AI providers (OpenAI, Anthropic) as fallback"
        ]
    }
    
    print("\n📋 Quota Management Recommendations:")
    print("-" * 40)
    
    print("\n🚀 Immediate Actions:")
    for action in recommendations["immediate_actions"]:
        print(f"  • {action}")
    
    print("\n🔮 Long-term Solutions:")
    for solution in recommendations["long_term_solutions"]:
        print(f"  • {solution}")
    
    print("\n🔄 Alternative Approaches:")
    for approach in recommendations["alternative_approaches"]:
        print(f"  • {approach}")
    
    return {"status": "success", "recommendations": recommendations}


def create_quota_safe_env() -> None:
    """Create a .env file with quota-safe settings."""
    
    env_content = """# Quota-Safe Configuration
# This configuration disables LLM qualification to avoid quota issues

# API Keys (keep your existing keys)
GROQ_API_KEY=your_groq_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
SCRAPEGRAPH_API_KEY=your_scrapegraph_api_key_here

# Database Configuration
DATABASE_URL=your_database_url_here
NEON_DATABASE_URL=your_neon_database_url_here

# Quota-Safe Settings
ENABLE_LLM_QUALIFICATION=false
USE_FALLBACK_QUALIFICATION=true
LLM_CONCURRENCY_LIMIT=1

# Other Settings
MAX_LEADS_PER_SEARCH=25
LEAD_QUALIFICATION_THRESHOLD=0.6
SEARCH_DELAY_SECONDS=3
DEBUG=false
LOG_LEVEL=INFO
"""
    
    env_file = ".env.quota-safe"
    with open(env_file, "w") as f:
        f.write(env_content)
    
    print(f"✅ Created quota-safe configuration file: {env_file}")
    print("📝 Please copy your actual API keys and database URLs to this file")
    print("🔄 To use this configuration, rename it to .env or set ENV_FILE=.env.quota-safe")


def show_current_settings() -> None:
    """Show current configuration settings."""
    
    print("⚙️ Current Configuration Settings:")
    print("=" * 40)
    
    settings_dict = {
        "LLM Qualification": settings.enable_llm_qualification,
        "Fallback Qualification": settings.use_fallback_qualification,
        "LLM Concurrency Limit": settings.llm_concurrency_limit,
        "Max Leads Per Search": settings.max_leads_per_search,
        "Qualification Threshold": settings.lead_qualification_threshold,
        "Search Delay (seconds)": settings.search_delay_seconds,
        "Debug Mode": settings.debug,
        "Log Level": settings.log_level
    }
    
    for setting, value in settings_dict.items():
        print(f"  {setting}: {value}")


def main():
    """Main function to handle quota management commands."""
    
    parser = argparse.ArgumentParser(description="Manage API quota and configuration")
    parser.add_argument("action", choices=["check", "safe-env", "settings"], 
                       help="Action to perform")
    
    args = parser.parse_args()
    
    if args.action == "check":
        check_quota_status()
    elif args.action == "safe-env":
        create_quota_safe_env()
    elif args.action == "settings":
        show_current_settings()


if __name__ == "__main__":
    main() 