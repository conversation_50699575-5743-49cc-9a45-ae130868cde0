#!/usr/bin/env python3
"""
Script to fix database schema issues.
"""

import asyncio
import sys
import os
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import settings
from database import db_manager


async def check_database_schema():
    """Check if the database schema is up to date."""
    
    print("🔍 Checking database schema...")
    
    await db_manager.initialize()
    
    async with db_manager.engine.begin() as conn:
        # Check if search_session_id column exists
        result = await conn.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'leads' 
            AND column_name = 'search_session_id'
        """))
        
        column_exists = result.fetchone() is not None
        
        if column_exists:
            print("✅ search_session_id column exists")
        else:
            print("❌ search_session_id column missing")
            
        # Check table structure
        result = await conn.execute(text("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'leads'
            ORDER BY ordinal_position
        """))
        
        columns = result.fetchall()
        print(f"\n📋 Current leads table columns ({len(columns)}):")
        for col in columns:
            print(f"  • {col[0]} ({col[1]}, nullable: {col[2]})")
        
        return column_exists


async def add_missing_columns():
    """Add missing columns to the database."""
    
    print("\n🔧 Adding missing columns...")
    
    await db_manager.initialize()
    
    async with db_manager.engine.begin() as conn:
        # Check if search_session_id column exists
        result = await conn.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'leads' 
            AND column_name = 'search_session_id'
        """))
        
        if result.fetchone() is None:
            print("➕ Adding search_session_id column...")
            await conn.execute(text("""
                ALTER TABLE leads 
                ADD COLUMN search_session_id INTEGER
            """))
            print("✅ search_session_id column added")
        else:
            print("✅ search_session_id column already exists")
        
        # Check if search_sessions table exists
        result = await conn.execute(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_name = 'search_sessions'
        """))
        
        if result.fetchone() is None:
            print("➕ Creating search_sessions table...")
            await conn.execute(text("""
                CREATE TABLE search_sessions (
                    id SERIAL PRIMARY KEY,
                    query VARCHAR(500) NOT NULL,
                    industry VARCHAR(100) NOT NULL,
                    leads_found INTEGER DEFAULT 0,
                    leads_qualified INTEGER DEFAULT 0,
                    started_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP WITHOUT TIME ZONE,
                    status VARCHAR(50) DEFAULT 'running',
                    session_metadata JSON
                )
            """))
            print("✅ search_sessions table created")
        else:
            print("✅ search_sessions table already exists")


async def recreate_tables():
    """Recreate all tables with current schema."""
    
    print("\n🔄 Recreating tables with current schema...")
    
    await db_manager.initialize()
    
    async with db_manager.engine.begin() as conn:
        # Drop existing tables
        print("🗑️ Dropping existing tables...")
        await conn.execute(text("DROP TABLE IF EXISTS leads CASCADE"))
        await conn.execute(text("DROP TABLE IF EXISTS search_sessions CASCADE"))
        
        # Create tables with current schema
        print("🏗️ Creating tables with current schema...")
        from models import Base
        await conn.run_sync(Base.metadata.create_all)
        
        print("✅ Tables recreated successfully")


async def main():
    """Main function to fix database issues."""
    
    import argparse
    
    parser = argparse.ArgumentParser(description="Fix database schema issues")
    parser.add_argument("action", choices=["check", "fix", "recreate"], 
                       help="Action to perform")
    
    args = parser.parse_args()
    
    if args.action == "check":
        await check_database_schema()
    elif args.action == "fix":
        await add_missing_columns()
    elif args.action == "recreate":
        await recreate_tables()
    
    await db_manager.close()


if __name__ == "__main__":
    asyncio.run(main()) 