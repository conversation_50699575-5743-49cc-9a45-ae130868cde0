"""Lead Generation Workflow using LangGraph."""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from models import WorkflowState, Industry, LeadStatus
from services.search_service import SearchService
from services.scraping_service import ScrapingService
from services.qualification_service import QualificationService
from database import LeadRepository, SearchSessionRepository
from config import settings

logger = logging.getLogger(__name__)


class LeadGenerationWorkflow:
    """Main workflow for lead generation using LangGraph."""
    
    def __init__(self):
        self.search_service = SearchService()
        self.scraping_service = ScrapingService()
        self.qualification_service = QualificationService()
        
        # Memory saver for checkpointing
        self.memory_saver = MemorySaver()
        
        # Create the workflow graph
        self.workflow = self._create_workflow()
    
    def _create_workflow(self) -> StateGraph:
        """Create the LangGraph workflow."""
        
        # Create the state graph
        workflow = StateGraph(WorkflowState)
        
        # Add nodes
        workflow.add_node("search_leads", self._search_leads_node)
        workflow.add_node("scrape_companies", self._scrape_companies_node)
        workflow.add_node("qualify_leads", self._qualify_leads_node)
        workflow.add_node("store_leads", self._store_leads_node)
        workflow.add_node("handle_errors", self._handle_errors_node)
        
        # Define the workflow edges
        workflow.set_entry_point("search_leads")
        
        # Add conditional edges
        workflow.add_conditional_edges(
            "search_leads",
            self._should_scrape,
            {
                "scrape": "scrape_companies",
                "skip_scrape": "qualify_leads",
                "error": "handle_errors"
            }
        )
        
        workflow.add_conditional_edges(
            "scrape_companies",
            self._should_qualify,
            {
                "qualify": "qualify_leads",
                "skip_qualify": "store_leads",
                "error": "handle_errors"
            }
        )
        
        workflow.add_conditional_edges(
            "qualify_leads",
            self._should_store,
            {
                "store": "store_leads",
                "skip_store": END,
                "error": "handle_errors"
            }
        )
        
        workflow.add_edge("store_leads", END)
        workflow.add_edge("handle_errors", END)
        
        return workflow.compile(checkpointer=self.memory_saver)
    
    async def _search_leads_node(self, state: WorkflowState) -> WorkflowState:
        """Node for searching leads using Tavily."""
        try:
            logger.info(f"Starting lead search for query: {state.query}")
            
            # Perform search
            search_results = await self.search_service.search_leads(
                query=state.query,
                industry=state.industry.value,
                max_results=settings.max_leads_per_search
            )
            
            # Update state
            state.search_results = search_results
            state.metadata["search_completed"] = datetime.utcnow().isoformat()
            state.metadata["search_results_count"] = len(search_results)
            
            logger.info(f"Search completed. Found {len(search_results)} potential leads")
            
            return state
            
        except Exception as e:
            logger.error(f"Error in search_leads_node: {e}")
            state.errors.append(f"Search error: {str(e)}")
            return state
    
    async def _scrape_companies_node(self, state: WorkflowState) -> WorkflowState:
        """Node for scraping company websites."""
        try:
            logger.info("Starting company scraping")
            
            if not state.search_results:
                logger.warning("No search results to scrape")
                return state
            
            # Prepare companies for scraping
            companies_to_scrape = []
            for result in state.search_results:
                if result.get("website"):
                    companies_to_scrape.append(result)
            
            if not companies_to_scrape:
                logger.warning("No companies with websites to scrape")
                return state
            
            # Scrape companies
            scraped_data = await self.scraping_service.scrape_multiple_companies(
                companies_to_scrape
            )
            
            # Merge scraped data with search results
            merged_leads = self._merge_search_and_scraped_data(
                state.search_results, scraped_data
            )
            
            # Update state
            state.scraped_leads = merged_leads
            state.metadata["scraping_completed"] = datetime.utcnow().isoformat()
            state.metadata["scraped_companies_count"] = len(scraped_data)
            
            logger.info(f"Scraping completed. Processed {len(scraped_data)} companies")
            
            return state
            
        except Exception as e:
            logger.error(f"Error in scrape_companies_node: {e}")
            state.errors.append(f"Scraping error: {str(e)}")
            return state
    
    async def _qualify_leads_node(self, state: WorkflowState) -> WorkflowState:
        """Node for qualifying leads using Gemini LLM."""
        try:
            logger.info("Starting lead qualification")
            
            # Determine which leads to qualify
            leads_to_qualify = state.scraped_leads if state.scraped_leads else state.search_results
            
            if not leads_to_qualify:
                logger.warning("No leads to qualify")
                return state
            
            # Qualify leads
            qualification_results = await self.qualification_service.batch_qualify_leads(
                leads_to_qualify,
                state.industry.value,
                batch_size=10
            )
            
            # Filter qualified leads
            qualified_leads = []
            for i, lead in enumerate(leads_to_qualify):
                if i < len(qualification_results):
                    qualification = qualification_results[i]
                    if qualification.get("is_qualified", False):
                        # Merge lead data with qualification results
                        qualified_lead = {**lead, **qualification}
                        qualified_leads.append(qualified_lead)
            
            # Update state
            state.qualified_leads = qualified_leads
            state.metadata["qualification_completed"] = datetime.utcnow().isoformat()
            state.metadata["qualified_leads_count"] = len(qualified_leads)
            state.metadata["qualification_summary"] = self.qualification_service.get_qualification_summary(
                qualification_results
            )
            
            logger.info(f"Qualification completed. {len(qualified_leads)} leads qualified")
            
            return state
            
        except Exception as e:
            logger.error(f"Error in qualify_leads_node: {e}")
            state.errors.append(f"Qualification error: {str(e)}")
            return state
    
    async def _store_leads_node(self, state: WorkflowState) -> WorkflowState:
        """Node for storing qualified leads in database."""
        try:
            logger.info("Starting lead storage")
            
            if not state.qualified_leads:
                logger.warning("No qualified leads to store")
                return state
            
            # Get database session
            from database import db_manager
            async for session in db_manager.get_session():
                lead_repo = LeadRepository(session)
                session_repo = SearchSessionRepository(session)
                
                # Create search session
                session_data = {
                    "query": state.query,
                    "industry": state.industry.value,
                    "leads_found": len(state.search_results),
                    "leads_qualified": len(state.qualified_leads),
                    "status": "completed",
                    "completed_at": datetime.utcnow(),
                    "metadata": state.metadata
                }
                
                search_session = await session_repo.create_session(session_data)
                
                # Store qualified leads
                stored_lead_ids = []
                for lead_data in state.qualified_leads:
                    try:
                        # Prepare lead data for storage
                        lead_create_data = self._prepare_lead_for_storage(lead_data)
                        
                        # Create lead
                        lead = await lead_repo.create_lead(lead_create_data)
                        stored_lead_ids.append(lead.id)
                        
                    except Exception as e:
                        logger.error(f"Error storing lead {lead_data.get('company_name', 'Unknown')}: {e}")
                        state.errors.append(f"Storage error for {lead_data.get('company_name', 'Unknown')}: {str(e)}")
                
                # Update search session with final counts
                await session_repo.update_session(
                    search_session.id,
                    {
                        "leads_qualified": len(stored_lead_ids),
                        "metadata": {
                            **state.metadata,
                            "stored_lead_ids": stored_lead_ids
                        }
                    }
                )
                
                # Update state
                state.stored_leads = stored_lead_ids
                state.metadata["storage_completed"] = datetime.utcnow().isoformat()
                state.metadata["stored_leads_count"] = len(stored_lead_ids)
                state.metadata["search_session_id"] = search_session.id
                
                logger.info(f"Storage completed. Stored {len(stored_lead_ids)} leads")
                break
            
            return state
            
        except Exception as e:
            logger.error(f"Error in store_leads_node: {e}")
            state.errors.append(f"Storage error: {str(e)}")
            return state
    
    async def _handle_errors_node(self, state: WorkflowState) -> WorkflowState:
        """Node for handling workflow errors."""
        logger.error(f"Workflow errors: {state.errors}")
        
        # Log errors and update metadata
        state.metadata["errors"] = state.errors
        state.metadata["error_handled"] = datetime.utcnow().isoformat()
        
        return state
    
    def _should_scrape(self, state: WorkflowState) -> str:
        """Determine if scraping should be performed."""
        if state.errors:
            return "error"
        
        if not state.search_results:
            return "skip_scrape"
        
        # Check if any search results have websites
        has_websites = any(result.get("website") for result in state.search_results)
        
        return "scrape" if has_websites else "skip_scrape"
    
    def _should_qualify(self, state: WorkflowState) -> str:
        """Determine if qualification should be performed."""
        if state.errors:
            return "error"
        
        leads_to_qualify = state.scraped_leads if state.scraped_leads else state.search_results
        
        if not leads_to_qualify:
            return "skip_qualify"
        
        return "qualify"
    
    def _should_store(self, state: WorkflowState) -> str:
        """Determine if leads should be stored."""
        if state.errors:
            return "error"
        
        if not state.qualified_leads:
            return "skip_store"
        
        return "store"
    
    def _merge_search_and_scraped_data(
        self, 
        search_results: List[Dict[str, Any]], 
        scraped_data: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Merge search results with scraped data."""
        merged_leads = []
        
        # Create lookup for scraped data by website
        scraped_lookup = {}
        for scraped in scraped_data:
            website = scraped.get("website", "")
            if website:
                scraped_lookup[website.lower()] = scraped
        
        # Merge data
        for search_result in search_results:
            website = search_result.get("website", "")
            scraped_info = scraped_lookup.get(website.lower(), {})
            
            # Merge search and scraped data
            merged_lead = {**search_result}
            
            # Update with scraped data if available
            if scraped_info:
                merged_lead.update({
                    "email": scraped_info.get("email") or merged_lead.get("email"),
                    "phone": scraped_info.get("phone") or merged_lead.get("phone"),
                    "location": scraped_info.get("location") or merged_lead.get("location"),
                    "services": scraped_info.get("services") or merged_lead.get("services"),
                    "description": scraped_info.get("description") or merged_lead.get("description"),
                    "raw_scraped_data": scraped_info.get("raw_scraped_data", {}),
                    "scraping_metadata": scraped_info.get("scraping_metadata", {})
                })
            
            merged_leads.append(merged_lead)
        
        return merged_leads
    
    def _prepare_lead_for_storage(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare lead data for database storage."""
        from models import LeadSource, LeadStatus
        
        return {
            "company_name": lead_data.get("company_name", ""),
            "website": lead_data.get("website"),
            "email": lead_data.get("email"),
            "phone": lead_data.get("phone"),
            "industry": lead_data.get("industry", ""),
            "services": lead_data.get("services"),
            "description": lead_data.get("description"),
            "location": lead_data.get("location"),
            "employee_count": lead_data.get("employee_count"),
            "revenue_range": lead_data.get("revenue_range"),
            "status": LeadStatus.QUALIFIED.value if lead_data.get("is_qualified") else LeadStatus.DISCOVERED.value,
            "qualification_score": lead_data.get("score"),
            "qualification_reason": lead_data.get("reasoning"),
            "source": LeadSource.WEB_SEARCH.value,
            "source_url": lead_data.get("source_url"),
            "search_query": lead_data.get("search_query"),
            "raw_data": {
                "search_data": lead_data.get("raw_data", {}),
                "scraped_data": lead_data.get("raw_scraped_data", {}),
                "qualification_data": {
                    "strengths": lead_data.get("strengths", []),
                    "weaknesses": lead_data.get("weaknesses", []),
                    "recommendations": lead_data.get("recommendations", []),
                    "key_factors": lead_data.get("key_factors", [])
                }
            },
            "notes": f"Qualified with score: {lead_data.get('score', 0)}"
        }
    
    async def run_workflow(
        self, 
        query: str, 
        industry: Industry,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Run the complete lead generation workflow.
        
        Args:
            query: Search query
            industry: Target industry
            config: Optional configuration overrides
        
        Returns:
            Workflow results
        """
        try:
            # Create initial state
            initial_state = WorkflowState(
                query=query,
                industry=industry,
                metadata={
                    "workflow_started": datetime.utcnow().isoformat(),
                    "config": config or {}
                }
            )
            
            # Run the workflow
            logger.info(f"Starting lead generation workflow for: {query} in {industry.value}")
            
            final_state = await self.workflow.ainvoke(
                initial_state,
                config=config or {}
            )
            
            # Prepare results
            results = {
                "query": final_state.query,
                "industry": final_state.industry.value,
                "search_results_count": len(final_state.search_results),
                "scraped_leads_count": len(final_state.scraped_leads),
                "qualified_leads_count": len(final_state.qualified_leads),
                "stored_leads_count": len(final_state.stored_leads),
                "errors": final_state.errors,
                "metadata": final_state.metadata,
                "success": len(final_state.errors) == 0
            }
            
            logger.info(f"Workflow completed. Results: {results}")
            
            return results
            
        except Exception as e:
            logger.error(f"Workflow execution error: {e}")
            return {
                "query": query,
                "industry": industry.value,
                "errors": [str(e)],
                "success": False
            }
    
    async def get_workflow_status(self, session_id: str) -> Dict[str, Any]:
        """Get the status of a workflow session."""
        try:
            # Retrieve session from memory
            session = await self.memory_saver.get(session_id)
            
            if not session:
                return {"error": "Session not found"}
            
            return {
                "session_id": session_id,
                "state": session.get("state"),
                "metadata": session.get("metadata", {})
            }
            
        except Exception as e:
            logger.error(f"Error getting workflow status: {e}")
            return {"error": str(e)} 