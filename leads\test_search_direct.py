"""Direct test of the search service to debug contact extraction issues."""

import asyncio
import logging
from services.search_service import SearchService
from config import settings

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_search_service():
    """Test the search service directly."""
    
    try:
        search_service = SearchService()
        
        print("Testing search service with forex query...")
        print(f"Tavily API Key configured: {bool(settings.tavily_api_key)}")
        
        # Test search
        results = await search_service.search_leads(
            query="funded trading programs forex traders",
            industry="forex",
            max_results=10
        )
        
        print(f"\nSearch completed. Found {len(results)} results:")
        
        for i, result in enumerate(results):
            print(f"\n--- Result {i+1} ---")
            print(f"Company: {result.get('company_name', 'N/A')}")
            print(f"Website: {result.get('website', 'N/A')}")
            print(f"Email: {result.get('email', 'N/A')}")
            print(f"Phone: {result.get('phone', 'N/A')}")
            print(f"Location: {result.get('location', 'N/A')}")
            print(f"Has Contact Info: {result.get('has_contact_info', False)}")
            print(f"Source URL: {result.get('source_url', 'N/A')}")
            print(f"Description: {result.get('description', 'N/A')[:100]}...")
        
        # Count results with contact info
        with_email = [r for r in results if r.get('email')]
        with_phone = [r for r in results if r.get('phone')]
        with_contact = [r for r in results if r.get('has_contact_info')]
        
        print(f"\n--- Summary ---")
        print(f"Total results: {len(results)}")
        print(f"With email: {len(with_email)}")
        print(f"With phone: {len(with_phone)}")
        print(f"With any contact info: {len(with_contact)}")
        
        if with_email:
            print(f"\nEmails found:")
            for result in with_email:
                print(f"  - {result['company_name']}: {result['email']}")
        
        if with_phone:
            print(f"\nPhones found:")
            for result in with_phone:
                print(f"  - {result['company_name']}: {result['phone']}")
        
    except Exception as e:
        print(f"Error testing search service: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_search_service())
