# Funded Traders Service

A specialized AI agent for discovering and qualifying funded traders (Foxex, prop firm traders) with enhanced search capabilities across multiple platforms.

## 🎯 Overview

The Funded Traders Service is designed to find and qualify funded traders across various platforms including:
- **Reddit** (r/Forex, r/Trading communities)
- **Facebook** (trading groups)
- **Instagram** (trader profiles)
- **Specific Prop Firms** (FTMO, MyForexFunds, Apex, etc.)
- **Foxex Platform** (specialized searches)

## 🚀 Features

### Advanced Search Capabilities
- **Multi-platform search**: Reddit, Facebook, Instagram, general web
- **Prop firm specific**: FTMO, MyForexFunds, Apex Trader Funding, Topstep, etc.
- **Foxex focused**: Specialized searches for Foxex platform traders
- **Contact extraction**: Email, phone, social media profiles
- **Performance tracking**: Profit percentages, funding amounts

### Specialized Qualification
- **Contact Quality** (30%): Email, phone, social profiles
- **Platform Reputation** (25%): Established prop firms, Foxex usage
- **Funding Amount** (20%): Higher amounts preferred ($50k+)
- **Performance Indicators** (15%): Profit percentages, success stories
- **Professional Presence** (10%): Social media activity, community engagement

### Data Extraction
- **Trader names** from various formats
- **Funding amounts** ($50k, $100k, etc.)
- **Trading platforms** and prop firms
- **Performance metrics** (profit percentages)
- **Contact information** (email, phone)
- **Social media profiles** (@username, platform links)
- **Geographic location** from content

## 📋 API Endpoints

### 1. Search Funded Traders
```http
POST /funded-traders/search
```

**Request:**
```json
{
  "max_results": 100,
  "search_depth": "advanced",
  "include_qualification": true,
  "batch_size": 5
}
```

**Response:**
```json
{
  "traders": [
    {
      "trader_name": "John Smith",
      "email": "<EMAIL>",
      "phone": "******-123-4567",
      "platform": "FTMO",
      "funding_amount": "$100k",
      "social_profiles": ["@johnsmith", "instagram.com/johnsmith"],
      "location": "New York",
      "performance": "15%",
      "source_url": "https://example.com/trader",
      "content_snippet": "John Smith is a successful funded trader...",
      "lead_type": "funded_trader"
    }
  ],
  "total_found": 25,
  "search_queries_used": ["site:reddit.com funded trader...", ...],
  "search_summary": {
    "platforms_found": ["FTMO", "MyForexFunds"],
    "funding_amounts": ["$50k", "$100k", "$200k"],
    "locations": ["New York", "Los Angeles"],
    "social_profiles_count": 45
  }
}
```

### 2. Qualify Individual Trader
```http
POST /funded-traders/qualify
```

**Request:**
```json
{
  "trader_data": {
    "trader_name": "John Smith",
    "platform": "FTMO",
    "funding_amount": "$100k",
    "email": "<EMAIL>",
    "phone": "******-123-4567",
    "location": "New York",
    "performance": "15%",
    "social_profiles": ["@johnsmith"],
    "source_url": "https://example.com/trader",
    "content_snippet": "John Smith is a successful funded trader..."
  }
}
```

**Response:**
```json
{
  "trader_name": "John Smith",
  "platform": "FTMO",
  "funding_amount": "$100k",
  "score": 0.85,
  "reasoning": "High-quality lead with complete contact information...",
  "strengths": ["Valid email address", "Reputable platform", "High funding amount"],
  "weaknesses": ["Limited social media presence"],
  "recommendations": ["Verify contact information", "Check trading performance"],
  "confidence": 0.9,
  "key_factors": ["Contact quality", "Platform reputation", "Funding level"],
  "contact_quality": "High",
  "platform_reputation": "High",
  "funding_level": "High",
  "is_qualified": true,
  "qualification_threshold": 0.7
}
```

### 3. Batch Qualification
```http
POST /funded-traders/qualify-batch
```

**Request:**
```json
[
  {
    "trader_name": "Alice Johnson",
    "platform": "MyForexFunds",
    "funding_amount": "$50k",
    "email": "<EMAIL>"
  },
  {
    "trader_name": "Bob Wilson",
    "platform": "Foxex",
    "funding_amount": "$200k",
    "email": "<EMAIL>"
  }
]
```

### 4. Get Search Queries
```http
GET /funded-traders/search-queries
```

**Response:**
```json
{
  "total_queries": 45,
  "categorized_queries": {
    "reddit": ["site:reddit.com funded trader...", ...],
    "facebook": ["site:facebook.com funded trader...", ...],
    "instagram": ["site:instagram.com funded trader...", ...],
    "foxex": ["Foxex funded trader contact information", ...],
    "prop_firms": ["FTMO funded trader contact information", ...],
    "general": ["funded trader prop firm success...", ...]
  },
  "all_queries": [...]
}
```

### 5. Get Supported Platforms
```http
GET /funded-traders/platforms
```

**Response:**
```json
{
  "total_platforms": 17,
  "platforms": [
    "Foxex", "FTMO", "MyForexFunds", "Apex Trader Funding", "Topstep",
    "Earn2Trade", "FundedNext", "The5ers", "SurgeTrader", "City Traders Imperium",
    "Traders Central", "Traders With Edge", "True Forex Funds", "Blue Guardian",
    "Trading Fund", "Traders Global", "Traders Academy"
  ],
  "major_platforms": ["Foxex", "FTMO", "MyForexFunds", "Apex Trader Funding", "Topstep"],
  "description": "These are the trading platforms and prop firms that the service searches for and recognizes when extracting funded trader information."
}
```

## 🔧 Usage Examples

### Python Service Usage

```python
import asyncio
from services.funded_traders_service import FundedTradersService

async def main():
    # Initialize service
    service = FundedTradersService()
    
    # Search for funded traders
    traders = await service.search_funded_traders(max_results=50)
    print(f"Found {len(traders)} traders")
    
    # Qualify individual trader
    qualification = await service.qualify_funded_trader(traders[0])
    print(f"Qualification score: {qualification['score']}")
    
    # Batch qualification
    results = await service.batch_qualify_funded_traders(traders, batch_size=5)
    
    # Get summary
    summary = service.get_qualification_summary(results)
    print(f"Qualified: {summary['qualified']}/{summary['total']}")

asyncio.run(main())
```

### API Usage with curl

```bash
# Search for funded traders
curl -X POST "http://localhost:8000/funded-traders/search" \
  -H "Content-Type: application/json" \
  -d '{
    "max_results": 20,
    "search_depth": "advanced",
    "include_qualification": true
  }'

# Qualify a specific trader
curl -X POST "http://localhost:8000/funded-traders/qualify" \
  -H "Content-Type: application/json" \
  -d '{
    "trader_data": {
      "trader_name": "John Smith",
      "platform": "FTMO",
      "funding_amount": "$100k",
      "email": "<EMAIL>"
    }
  }'

# Get search queries
curl "http://localhost:8000/funded-traders/search-queries"

# Get supported platforms
curl "http://localhost:8000/funded-traders/platforms"
```

## 🧪 Testing

Run the test suite to verify functionality:

```bash
# Test the service directly
python test_funded_traders.py

# Start the API server
python -m uvicorn api.main:app --reload

# Test API endpoints (in another terminal)
python test_funded_traders.py
```

## 📊 Search Queries Breakdown

The service uses **45+ specialized search queries** across multiple platforms:

### Reddit Searches (7 queries)
- `site:reddit.com funded trader prop firm success story`
- `site:reddit.com r/Forex funded trader account`
- `site:reddit.com r/Trading funded trader results`
- `site:reddit.com prop firm challenge success`
- `site:reddit.com funded trader contact information`
- `site:reddit.com forex funded trader email phone`
- `site:reddit.com prop firm trader contact details`

### Facebook Searches (5 queries)
- `site:facebook.com funded trader group`
- `site:facebook.com forex traders contact`
- `site:facebook.com prop firm traders`
- `site:facebook.com funded trader success`
- `site:facebook.com forex trading contact information`

### Instagram Searches (4 queries)
- `site:instagram.com funded trader`
- `site:instagram.com prop firm success`
- `site:instagram.com forex trader contact`
- `site:instagram.com funded account trader`

### Foxex Specific (5 queries)
- `Foxex funded trader contact information`
- `Foxex prop firm success trader contact`
- `Foxex funded account trader email phone`
- `Foxex trading funded trader contact details`
- `Foxex prop firm challenge success contact`

### Prop Firm Specific (10 queries)
- `FTMO funded trader contact information`
- `MyForexFunds funded trader contact`
- `Apex Trader Funding contact information`
- `Topstep funded trader contact details`
- `Earn2Trade funded trader contact`
- `FundedNext funded trader contact information`
- `The5ers funded trader contact details`
- `SurgeTrader funded trader contact`
- `City Traders Imperium funded trader contact`
- `Traders Central funded trader contact information`

### General Searches (10 queries)
- `funded trader prop firm success contact information`
- `forex funded trader email phone contact details`
- `prop firm challenge success trader contact`
- `funded trading account success story contact`
- `forex prop firm funded trader contact information`
- `funded trader results contact email phone`
- `prop firm evaluation success trader contact`
- `forex funded account trader contact details`
- `funded trader program success contact information`
- `prop firm funded trader contact email phone`

## 🎯 Qualification Criteria

### Contact Information Quality (30%)
- Valid email address
- Phone number present
- Social media profiles available
- Professional contact details

### Trading Platform/Prop Firm (25%)
- Reputable prop firm (FTMO, MyForexFunds, Apex, etc.)
- Foxex platform usage
- Established trading platform
- Verified prop firm success

### Funding Amount (20%)
- Higher funding amounts preferred ($50k+)
- Multiple funded accounts
- Consistent funding history
- Professional funding levels

### Performance Indicators (15%)
- Profit percentages mentioned
- Trading success stories
- Consistent performance
- Risk management evidence

### Professional Presence (10%)
- Social media activity
- Trading community engagement
- Professional online presence
- Educational content sharing

## 🔍 Data Extraction Capabilities

### Trader Name Extraction
- `John Doe` (First Last format)
- `Trader John` (Trader prefix)
- `John is a funded trader` (Contextual)
- `funded trader John` (Reverse format)

### Funding Amount Extraction
- `$50k`, `$100k`, `$200k`
- `50k funded`, `100K funded`
- `funded 50k`, `funded 100K`

### Platform Recognition
- **Major Platforms**: Foxex, FTMO, MyForexFunds, Apex Trader Funding, Topstep
- **Other Platforms**: Earn2Trade, FundedNext, The5ers, SurgeTrader, City Traders Imperium, etc.

### Performance Metrics
- `15% profit`, `12% return`, `18% gain`
- `profit 15%`, `return 12%`

### Contact Information
- Email: Standard email pattern matching
- Phone: Multiple formats (************, (*************, ******-456-7890)
- Social: @username, platform.com/username patterns

## 🚀 Getting Started

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up environment variables**:
   ```bash
   export TAVILY_API_KEY="your_tavily_api_key"
   export GROQ_API_KEY="your_groq_api_key"
   ```

3. **Run the service**:
   ```bash
   # Start the API server
   python -m uvicorn api.main:app --reload
   
   # Test the service
   python test_funded_traders.py
   ```

4. **Access the API**:
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health
   - Funded Traders Endpoints: http://localhost:8000/funded-traders/

## 📈 Performance Tips

- Use `search_depth="basic"` for faster searches
- Set `max_results` to reasonable limits (50-100)
- Use `batch_size=5` for qualification to avoid rate limits
- Monitor API usage to stay within limits

## 🔧 Configuration

The service uses the following configuration from `config.py`:
- `tavily_api_key`: For search functionality
- `groq_api_key`: For LLM qualification
- `search_delay_seconds`: Rate limiting between searches
- `lead_qualification_threshold`: Qualification threshold (default: 0.7 for funded traders)

## 🤝 Contributing

To add new search queries or platforms:

1. **Add to search queries** in `_build_funded_trader_queries()`
2. **Add platform recognition** in `_extract_trading_platform()`
3. **Update qualification criteria** in `_create_funded_trader_qualification_prompt()`
4. **Test with sample data** using the test script

## 📝 License

This project is part of the Lead Generation AI Agent system. 