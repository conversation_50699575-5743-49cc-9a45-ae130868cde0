"""Test script for the Lead Generation API."""

import asyncio
import httpx
import json

async def test_search_api():
    """Test the search API endpoint."""
    
    # Test data
    search_data = {
        "query": "funded trading programs forex traders",
        "industry": "forex",
        "max_results": 5
    }
    
    try:
        async with httpx.AsyncClient() as client:
            print("Testing health endpoint...")
            health_response = await client.get("http://localhost:8000/health")
            print(f"Health check: {health_response.status_code} - {health_response.json()}")
            
            print("\nTesting search endpoint...")
            search_response = await client.post(
                "http://localhost:8000/api/v1/search",
                json=search_data,
                timeout=60.0
            )
            
            print(f"Search response: {search_response.status_code}")
            print(f"Response data: {json.dumps(search_response.json(), indent=2)}")
            
            if search_response.status_code == 200:
                response_data = search_response.json()
                session_id = response_data.get("session_id")
                
                if session_id:
                    print(f"\nWaiting for workflow to complete...")
                    await asyncio.sleep(20)  # Wait for background processing

                    print(f"Checking session status...")
                    status_response = await client.get(f"http://localhost:8000/api/v1/search/{session_id}")
                    print(f"Status response: {status_response.status_code}")
                    print(f"Status data: {json.dumps(status_response.json(), indent=2)}")

                    # Check multiple times if still running
                    for i in range(3):
                        if status_response.json().get("status") == "running":
                            print(f"Still running, waiting more... (attempt {i+1})")
                            await asyncio.sleep(10)
                            status_response = await client.get(f"http://localhost:8000/api/v1/search/{session_id}")
                            print(f"Status data: {json.dumps(status_response.json(), indent=2)}")
                        else:
                            break
            
    except Exception as e:
        print(f"Error testing API: {e}")

if __name__ == "__main__":
    asyncio.run(test_search_api())
