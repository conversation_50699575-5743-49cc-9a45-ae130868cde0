#!/usr/bin/env python3
"""Test script to verify the fixes for the lead generation system."""

import asyncio
import logging
from models import Industry
from workflow.lead_generation_workflow import LeadGenerationWorkflow

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_workflow():
    """Test the workflow with proper checkpointing."""
    try:
        logger.info("Testing lead generation workflow...")
        
        # Initialize workflow
        workflow = LeadGenerationWorkflow()
        logger.info("Workflow initialized successfully")
        
        # Test with a simple query
        results = await workflow.run_workflow(
            query="funded trading programs forex traders",
            industry=Industry.FOREX,
            config={"max_results": 5}
        )
        
        logger.info(f"Workflow test completed: {results}")
        
        if results["success"]:
            logger.info("✅ Workflow test passed!")
        else:
            logger.error(f"❌ Workflow test failed: {results['errors']}")
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        raise

async def test_database():
    """Test database connection and models."""
    try:
        logger.info("Testing database connection...")
        
        from database import db_manager
        await db_manager.initialize()
        logger.info("✅ Database initialized successfully")
        
        # Test creating a session
        from database import SearchSessionRepository
        async for session in db_manager.get_session():
            session_repo = SearchSessionRepository(session)
            
            test_session = await session_repo.create_session({
                "query": "test query",
                "industry": "forex",
                "status": "completed"
            })
            
            logger.info(f"✅ Test session created: {test_session.id}")
            break
            
    except Exception as e:
        logger.error(f"❌ Database test failed: {e}")
        raise

async def main():
    """Run all tests."""
    logger.info("Starting tests...")
    
    # Test database first
    await test_database()
    
    # Test workflow
    await test_workflow()
    
    logger.info("All tests completed!")

if __name__ == "__main__":
    asyncio.run(main()) 