#!/usr/bin/env python3
"""
Test script for Groq LLM integration.
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.qualification_service import QualificationService
from config import settings


async def test_groq_qualification():
    """Test Groq LLM qualification."""
    
    print("🧪 Testing Groq LLM Integration...")
    print("=" * 50)
    
    # Check if Groq API key is configured
    if not settings.groq_api_key:
        print("❌ GROQ_API_KEY not found in environment variables")
        print("💡 Please add your Groq API key to the .env file:")
        print("   GROQ_API_KEY=your_groq_api_key_here")
        return False
    
    print("✅ Groq API key configured")
    
    try:
        # Initialize qualification service
        qualification_service = QualificationService()
        
        # Test lead data
        test_lead = {
            "company_name": "Test Forex Trading Company",
            "website": "https://example.com",
            "description": "Leading forex trading platform with funded trader programs",
            "industry": "forex"
        }
        
        print("\n🔍 Testing lead qualification...")
        result = await qualification_service.qualify_lead(test_lead, "forex")
        
        print(f"✅ Qualification successful!")
        print(f"📊 Score: {result.get('score', 'N/A')}")
        print(f"🎯 Qualified: {result.get('is_qualified', 'N/A')}")
        print(f"💭 Reasoning: {result.get('reasoning', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Groq integration: {e}")
        return False


async def test_fallback_qualification():
    """Test fallback qualification when LLM is disabled."""
    
    print("\n🧪 Testing Fallback Qualification...")
    print("=" * 50)
    
    try:
        # Initialize qualification service with LLM disabled
        qualification_service = QualificationService()
        qualification_service.enable_llm_qualification = False
        
        # Test lead data
        test_lead = {
            "company_name": "Test Forex Trading Company",
            "website": "https://example.com",
            "description": "Leading forex trading platform with funded trader programs",
            "industry": "forex"
        }
        
        print("🔍 Testing fallback qualification...")
        result = await qualification_service.qualify_lead(test_lead, "forex")
        
        print(f"✅ Fallback qualification successful!")
        print(f"📊 Score: {result.get('score', 'N/A')}")
        print(f"🎯 Qualified: {result.get('is_qualified', 'N/A')}")
        print(f"💭 Reasoning: {result.get('reasoning', 'N/A')}")
        print(f"🔧 Method: {result.get('method', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing fallback qualification: {e}")
        return False


async def main():
    """Main test function."""
    
    print("🚀 Starting Groq Integration Tests...")
    print("=" * 60)
    
    # Test Groq LLM qualification
    groq_success = await test_groq_qualification()
    
    # Test fallback qualification
    fallback_success = await test_fallback_qualification()
    
    print("\n" + "=" * 60)
    print("📋 Test Results:")
    print(f"  • Groq LLM Qualification: {'✅ PASS' if groq_success else '❌ FAIL'}")
    print(f"  • Fallback Qualification: {'✅ PASS' if fallback_success else '❌ FAIL'}")
    
    if groq_success and fallback_success:
        print("\n🎉 All tests passed! Groq integration is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Please check the configuration.")
    
    return groq_success and fallback_success


if __name__ == "__main__":
    asyncio.run(main()) 