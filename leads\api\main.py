"""FastAPI application for the Lead Generation AI Agent."""

import asyncio
import logging
from typing import List, Optional, Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uuid

from models import SearchRequest, SearchResponse, LeadResponse, Industry
from workflow.simple_workflow import SimpleLeadGenerationWorkflow
from database import get_db_session, SearchSessionRepository, LeadRepository
from config import settings
from api.funded_traders import router as funded_traders_router

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Lead Generation AI Agent",
    description="AI-powered lead generation agent for forex traders and software houses",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include funded traders router
app.include_router(funded_traders_router)

# Global workflow instance
workflow = None

@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    global workflow
    workflow = SimpleLeadGenerationWorkflow()
    logger.info("Lead Generation AI Agent started successfully")

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Lead Generation AI Agent is running!"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "Lead Generation AI Agent"}

@app.post("/api/v1/search", response_model=SearchResponse)
async def search_leads(
    request: SearchRequest, 
    background_tasks: BackgroundTasks,
    db_session = Depends(get_db_session)
):
    """Initiate a lead search with background processing."""
    try:
        logger.info(f"Received search request: {request.query} for industry: {request.industry}")
        
        # Create search session
        session_repo = SearchSessionRepository(db_session)
        session_data = {
            "query": request.query,
            "industry": request.industry.value,
            "status": "running",
            "session_metadata": {
                "max_results": request.max_results,
                "include_contact_info": request.include_contact_info
            }
        }
        
        session = await session_repo.create_session(session_data)
        
        # Add background task to run workflow
        background_tasks.add_task(
            run_workflow_background,
            session.id,
            request.query,
            request.industry,
            request.max_results
        )
        
        return SearchResponse(
            session_id=session.id,
            query=request.query,
            industry=request.industry,
            total_leads_found=0,
            qualified_leads=0,
            status="running",
            leads=[],
            search_metadata={"session_id": session.id}
        )
        
    except Exception as e:
        logger.error(f"Error in search endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def run_workflow_background(
    session_id: int,
    query: str,
    industry: Industry,
    max_results: Optional[int] = None
):
    """Run the workflow in the background."""
    try:
        logger.info(f"Starting background workflow for session {session_id}")
        
        # Run the workflow
        results = await workflow.run_workflow(
            query=query,
            industry=industry,
            config={"max_results": max_results}
        )
        
        # Update session with results
        async for db_session in get_db_session():
            session_repo = SearchSessionRepository(db_session)
            lead_repo = LeadRepository(db_session)

            # Update session status - handle both success and error cases
            update_data = {
                "status": "completed" if results.get("success", False) else "failed",
                "leads_found": results.get("search_results_count", 0),
                "leads_qualified": results.get("qualified_leads_count", 0),
                "completed_at": results.get("metadata", {}).get("workflow_completed"),
                "session_metadata": results.get("metadata", {})
            }

            # Add error information if workflow failed
            if not results.get("success", False) and results.get("errors"):
                update_data["session_metadata"]["errors"] = results["errors"]

            await session_repo.update_session(session_id, update_data)

            logger.info(f"Background workflow completed for session {session_id}")
            
    except Exception as e:
        logger.error(f"Error in background workflow for session {session_id}: {e}")
        
        # Update session with error
        try:
            async for db_session in get_db_session():
                session_repo = SearchSessionRepository(db_session)
                await session_repo.update_session(session_id, {
                    "status": "failed",
                    "session_metadata": {"error": str(e)}
                })
        except Exception as update_error:
            logger.error(f"Error updating session {session_id}: {update_error}")

@app.get("/api/v1/search/{session_id}", response_model=SearchResponse)
async def get_search_status(session_id: int, db_session = Depends(get_db_session)):
    """Get the status of a search session."""
    try:
        session_repo = SearchSessionRepository(db_session)
        lead_repo = LeadRepository(db_session)
        
        session = await session_repo.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get leads for this session
        leads = await lead_repo.get_leads_by_session(session_id)
        lead_responses = [LeadResponse.from_orm(lead) for lead in leads]
        
        return SearchResponse(
            session_id=session.id,
            query=session.query,
            industry=Industry(session.industry),
            total_leads_found=session.leads_found,
            qualified_leads=session.leads_qualified,
            status=session.status,
            leads=lead_responses,
            search_metadata=session.session_metadata or {}
        )
        
    except Exception as e:
        logger.error(f"Error getting search status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/leads", response_model=List[LeadResponse])
async def get_leads(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    industry: Optional[str] = None,
    db_session = Depends(get_db_session)
):
    """Get all leads with optional filtering."""
    try:
        lead_repo = LeadRepository(db_session)
        leads = await lead_repo.get_leads(skip=skip, limit=limit, status=status, industry=industry)
        return [LeadResponse.from_orm(lead) for lead in leads]
        
    except Exception as e:
        logger.error(f"Error getting leads: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/leads/{lead_id}", response_model=LeadResponse)
async def get_lead(lead_id: int, db_session = Depends(get_db_session)):
    """Get a specific lead by ID."""
    try:
        lead_repo = LeadRepository(db_session)
        lead = await lead_repo.get_lead(lead_id)
        
        if not lead:
            raise HTTPException(status_code=404, detail="Lead not found")
        
        return LeadResponse.from_orm(lead)
        
    except Exception as e:
        logger.error(f"Error getting lead {lead_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 