"""Lead qualification service using Gemini LLM."""

import logging
from typing import List, Dict, Any, Optional
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.prompts import ChatPromptTemplate
from langchain.schema import HumanMessage, SystemMessage
from config import settings

logger = logging.getLogger(__name__)


class QualificationService:
    """Service for qualifying leads using Gemini LLM."""
    
    def __init__(self):
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-flash-1.5",
            google_api_key=settings.google_api_key,
            temperature=0.1,
            max_output_tokens=2048
        )
        self.qualification_threshold = settings.lead_qualification_threshold
    
    async def qualify_lead(
        self, 
        lead_data: Dict[str, Any],
        industry: str
    ) -> Dict[str, Any]:
        """
        Qualify a lead using Gemini LLM.
        
        Args:
            lead_data: Lead information
            industry: Target industry
        
        Returns:
            Qualification results with score and reasoning
        """
        try:
            # Prepare lead information for qualification
            lead_info = self._prepare_lead_info(lead_data)
            
            # Create qualification prompt
            prompt = self._create_qualification_prompt(lead_info, industry)
            
            # Get LLM response
            response = await self._get_qualification_response(prompt)
            
            # Parse qualification results
            qualification_result = self._parse_qualification_response(response)
            
            # Add metadata
            qualification_result.update({
                "lead_id": lead_data.get("id"),
                "company_name": lead_data.get("company_name"),
                "industry": industry,
                "qualification_threshold": self.qualification_threshold,
                "is_qualified": qualification_result.get("score", 0) >= self.qualification_threshold
            })
            
            return qualification_result
            
        except Exception as e:
            logger.error(f"Error qualifying lead {lead_data.get('company_name', 'Unknown')}: {e}")
            return self._create_fallback_qualification(lead_data, industry)
    
    async def qualify_multiple_leads(
        self, 
        leads: List[Dict[str, Any]],
        industry: str
    ) -> List[Dict[str, Any]]:
        """
        Qualify multiple leads concurrently.
        
        Args:
            leads: List of lead data
            industry: Target industry
        
        Returns:
            List of qualification results
        """
        import asyncio
        
        tasks = []
        for lead in leads:
            task = self.qualify_lead(lead, industry)
            tasks.append(task)
        
        # Execute qualification tasks with concurrency limit
        semaphore = asyncio.Semaphore(10)  # Limit concurrent LLM calls
        
        async def limited_qualify(task):
            async with semaphore:
                return await task
        
        results = await asyncio.gather(
            *[limited_qualify(task) for task in tasks],
            return_exceptions=True
        )
        
        # Filter out exceptions
        valid_results = []
        for result in results:
            if isinstance(result, dict):
                valid_results.append(result)
            elif isinstance(result, Exception):
                logger.error(f"Qualification error: {result}")
        
        return valid_results
    
    def _prepare_lead_info(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare lead information for qualification."""
        return {
            "company_name": lead_data.get("company_name", ""),
            "website": lead_data.get("website", ""),
            "email": lead_data.get("email", ""),
            "phone": lead_data.get("phone", ""),
            "location": lead_data.get("location", ""),
            "services": lead_data.get("services", ""),
            "description": lead_data.get("description", ""),
            "industry": lead_data.get("industry", ""),
            "employee_count": lead_data.get("employee_count", ""),
            "revenue_range": lead_data.get("revenue_range", ""),
            "source_url": lead_data.get("source_url", ""),
            "raw_data": lead_data.get("raw_data", {}),
            "scraped_data": lead_data.get("raw_scraped_data", {})
        }
    
    def _create_qualification_prompt(
        self, 
        lead_info: Dict[str, Any], 
        industry: str
    ) -> str:
        """Create qualification prompt for Gemini LLM."""
        
        # Industry-specific criteria
        industry_criteria = self._get_industry_criteria(industry)
        
        prompt_template = f"""
You are an expert lead qualification specialist for {industry} companies. Your task is to evaluate a potential lead and provide a qualification score from 0.0 to 1.0, where 1.0 is the highest quality lead.

Lead Information:
- Company Name: {lead_info['company_name']}
- Website: {lead_info['website']}
- Email: {lead_info['email']}
- Phone: {lead_info['phone']}
- Location: {lead_info['location']}
- Services: {lead_info['services']}
- Description: {lead_info['description']}
- Industry: {lead_info['industry']}
- Employee Count: {lead_info['employee_count']}
- Revenue Range: {lead_info['revenue_range']}

Industry: {industry}

Qualification Criteria for {industry}:
{industry_criteria}

Evaluation Guidelines:
1. Score from 0.0 to 1.0 (1.0 = highest quality)
2. Consider company size, contact information quality, service relevance
3. Evaluate potential for business relationship
4. Consider data completeness and accuracy

Please provide your evaluation in the following JSON format:
{{
    "score": 0.85,
    "reasoning": "Detailed explanation of the score",
    "strengths": ["List of positive factors"],
    "weaknesses": ["List of concerns or missing information"],
    "recommendations": ["Suggestions for follow-up or additional research"],
    "confidence": 0.9,
    "key_factors": ["Most important factors that influenced the score"]
}}

Focus on practical business value and potential for successful engagement.
"""
        
        return prompt_template
    
    def _get_industry_criteria(self, industry: str) -> str:
        """Get industry-specific qualification criteria."""
        
        criteria_map = {
            "healthcare": """
- Healthcare software or technology focus
- Medical device or pharmaceutical companies
- Healthcare IT solutions providers
- Digital health or telemedicine companies
- Medical practice management software
- Healthcare data analytics companies
- Clinical workflow optimization
- Patient engagement platforms
- Healthcare compliance and security
- Medical imaging or diagnostic software
""",
            "finance": """
- Fintech or financial technology companies
- Banking software or platforms
- Investment or trading technology
- Payment processing companies
- Financial data analytics
- Risk management software
- Compliance and regulatory technology
- Wealth management platforms
- Insurance technology companies
- Cryptocurrency or blockchain companies
""",
            "e-commerce": """
- E-commerce platforms or marketplaces
- Online retail technology
- Digital commerce solutions
- Shopping cart or payment systems
- Inventory management software
- Customer relationship management
- Digital marketing platforms
- Supply chain optimization
- Multi-channel retail solutions
- Mobile commerce applications
""",
            "forex": """
- Forex trading platforms or brokers
- Currency exchange services
- Trading technology providers
- Financial data providers
- Risk management for forex
- Trading analytics software
- Compliance for financial trading
- Mobile trading applications
- Algorithmic trading systems
- Forex education or training
""",
            "software": """
- Custom software development
- Enterprise software solutions
- SaaS or cloud-based platforms
- Technology consulting services
- Digital transformation services
- Software integration services
- IT infrastructure solutions
- Cybersecurity services
- Data analytics and BI
- Mobile app development
"""
        }
        
        return criteria_map.get(industry, """
- Technology or software focus
- Digital transformation potential
- Scalable business model
- Strong market presence
- Quality contact information
- Relevant service offerings
""")
    
    async def _get_qualification_response(self, prompt: str) -> str:
        """Get qualification response from Gemini LLM."""
        try:
            messages = [
                SystemMessage(content="You are an expert lead qualification specialist. Provide detailed, accurate evaluations in JSON format."),
                HumanMessage(content=prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"Error getting LLM response: {e}")
            raise
    
    def _parse_qualification_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response into structured qualification data."""
        try:
            import json
            import re
            
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                parsed_data = json.loads(json_str)
                
                # Validate and normalize score
                score = float(parsed_data.get("score", 0))
                score = max(0.0, min(1.0, score))  # Clamp between 0 and 1
                
                return {
                    "score": score,
                    "reasoning": parsed_data.get("reasoning", ""),
                    "strengths": parsed_data.get("strengths", []),
                    "weaknesses": parsed_data.get("weaknesses", []),
                    "recommendations": parsed_data.get("recommendations", []),
                    "confidence": float(parsed_data.get("confidence", 0.5)),
                    "key_factors": parsed_data.get("key_factors", []),
                    "raw_response": response
                }
            else:
                # Fallback parsing
                return self._fallback_parse_response(response)
                
        except Exception as e:
            logger.error(f"Error parsing qualification response: {e}")
            return self._fallback_parse_response(response)
    
    def _fallback_parse_response(self, response: str) -> Dict[str, Any]:
        """Fallback parsing for LLM response."""
        # Simple score extraction
        import re
        
        # Look for score in response
        score_match = re.search(r'score["\s]*:["\s]*([0-9]*\.?[0-9]+)', response, re.IGNORECASE)
        score = float(score_match.group(1)) if score_match else 0.5
        
        return {
            "score": score,
            "reasoning": response[:500],
            "strengths": [],
            "weaknesses": [],
            "recommendations": [],
            "confidence": 0.5,
            "key_factors": [],
            "raw_response": response,
            "parse_error": True
        }
    
    def _create_fallback_qualification(
        self, 
        lead_data: Dict[str, Any], 
        industry: str
    ) -> Dict[str, Any]:
        """Create fallback qualification when LLM fails."""
        return {
            "score": 0.5,  # Neutral score
            "reasoning": "Automatic fallback qualification due to processing error",
            "strengths": [],
            "weaknesses": ["Unable to perform detailed qualification"],
            "recommendations": ["Manual review recommended"],
            "confidence": 0.3,
            "key_factors": [],
            "lead_id": lead_data.get("id"),
            "company_name": lead_data.get("company_name"),
            "industry": industry,
            "qualification_threshold": self.qualification_threshold,
            "is_qualified": False,
            "fallback": True
        }
    
    async def batch_qualify_leads(
        self, 
        leads: List[Dict[str, Any]], 
        industry: str,
        batch_size: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Qualify leads in batches to manage API rate limits.
        
        Args:
            leads: List of leads to qualify
            industry: Target industry
            batch_size: Number of leads to process per batch
        
        Returns:
            List of qualification results
        """
        all_results = []
        
        for i in range(0, len(leads), batch_size):
            batch = leads[i:i + batch_size]
            batch_results = await self.qualify_multiple_leads(batch, industry)
            all_results.extend(batch_results)
            
            # Add delay between batches
            if i + batch_size < len(leads):
                import asyncio
                await asyncio.sleep(1)
        
        return all_results
    
    def get_qualification_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics for qualification results."""
        if not results:
            return {}
        
        qualified_count = sum(1 for r in results if r.get("is_qualified", False))
        total_count = len(results)
        avg_score = sum(r.get("score", 0) for r in results) / total_count
        
        score_distribution = {
            "high": sum(1 for r in results if r.get("score", 0) >= 0.8),
            "medium": sum(1 for r in results if 0.5 <= r.get("score", 0) < 0.8),
            "low": sum(1 for r in results if r.get("score", 0) < 0.5)
        }
        
        return {
            "total_leads": total_count,
            "qualified_leads": qualified_count,
            "qualification_rate": qualified_count / total_count if total_count > 0 else 0,
            "average_score": avg_score,
            "score_distribution": score_distribution,
            "threshold": self.qualification_threshold
        } 