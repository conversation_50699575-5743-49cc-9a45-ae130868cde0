# Quota Management Scripts

This directory contains scripts to help manage API quota limits and configuration.

## Quota Management Script

The `manage_quota.py` script provides tools to handle API quota issues, particularly with Groq's LLM API.

### Usage

```bash
# Check quota status and get recommendations
python scripts/manage_quota.py check

# Create a quota-safe environment configuration
python scripts/manage_quota.py safe-env

# Show current configuration settings
python scripts/manage_quota.py settings
```

### Commands

#### `check`
- Analyzes current configuration
- Provides immediate actions to resolve quota issues
- Suggests long-term solutions
- Recommends alternative approaches

#### `safe-env`
- Creates a `.env.quota-safe` file with conservative settings
- Disables LLM qualification to avoid API calls
- Enables fallback qualification using rule-based scoring
- Reduces concurrency limits

#### `settings`
- Shows current configuration values
- Helps identify which settings might be causing quota issues

### Immediate Solutions for Quota Issues

1. **Disable LLM Qualification Temporarily**
   ```bash
   # Add to your .env file
   ENABLE_LLM_QUALIFICATION=false
   USE_FALLBACK_QUALIFICATION=true
   ```

2. **Reduce Concurrency**
   ```bash
   # Add to your .env file
   LLM_CONCURRENCY_LIMIT=1
   ```

3. **Use Quota-Safe Configuration**
   ```bash
   # Run the script to create safe config
   python scripts/manage_quota.py safe-env
   
   # Copy your API keys to the new file and use it
   cp .env.quota-safe .env
   ```

### Long-term Solutions

1. **Upgrade Groq Plan**
   - Visit https://console.groq.com/
   - Upgrade to a paid plan for higher quotas

2. **Implement Caching**
   - Cache qualification results to avoid redundant API calls
   - Store results in database for reuse

3. **Use Multiple API Keys**
   - Rotate between multiple Groq API keys
   - Implement key rotation logic

4. **Alternative AI Providers**
   - Use OpenAI, Anthropic, or other providers as fallback
   - Implement provider switching logic

### Fallback Qualification

When LLM qualification is disabled, the system uses rule-based qualification:

- **Keyword Analysis**: Checks for relevant terms like "forex", "trading", "funded", etc.
- **Website Presence**: Boosts score if company has a website
- **Basic Scoring**: Provides reasonable qualification scores without API calls

This ensures the system continues to work even when API quotas are exceeded. 