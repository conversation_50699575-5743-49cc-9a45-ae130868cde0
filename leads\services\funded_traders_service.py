"""Specialized service for finding funded traders (Foxex, prop firm traders)."""

import asyncio
import logging
import re
from typing import List, Dict, Any, Optional
from tavily import TavilyClient
from langchain_groq import ChatGroq
from langchain.prompts import ChatPromptTemplate
from config import settings

logger = logging.getLogger(__name__)


class FundedTradersService:
    """Specialized service for finding and qualifying funded traders."""
    
    def __init__(self):
        self.search_client = TavilyClient(api_key=settings.tavily_api_key)
        self.llm = ChatGroq(
            model="llama3-8b-8192",
            groq_api_key=settings.groq_api_key,
            temperature=0.1,
            max_tokens=2048
        )
        self.qualification_threshold = 0.7  # Higher threshold for funded traders
    
    async def search_funded_traders(
        self,
        max_results: int = 100,
        search_depth: str = "advanced"
    ) -> List[Dict[str, Any]]:
        """
        Search for funded traders across multiple platforms.
        
        Args:
            max_results: Maximum number of results
            search_depth: Search depth (basic, advanced)
        
        Returns:
            List of funded trader leads
        """
        try:
            all_results = []
            
            # Search queries for different platforms and contexts
            search_queries = self._build_funded_trader_queries()
            
            for query in search_queries:
                logger.info(f"Searching for funded traders: {query}")
                
                # Perform search
                response = await self._perform_search(
                    query, max_results // len(search_queries), search_depth
                )
                
                # Process and filter results
                processed_results = self._process_funded_trader_results(response)
                all_results.extend(processed_results)
                
                # Add delay to respect rate limits
                await asyncio.sleep(settings.search_delay_seconds)
            
            # Remove duplicates and limit results
            unique_results = self._deduplicate_results(all_results)
            return unique_results[:max_results]
            
        except Exception as e:
            logger.error(f"Error in search_funded_traders: {e}")
            return []
    
    def _build_funded_trader_queries(self) -> List[str]:
        """Build specialized search queries for funded traders."""
        queries = []
        
        # Reddit searches
        reddit_queries = [
            "site:reddit.com funded trader prop firm success story",
            "site:reddit.com r/Forex funded trader account",
            "site:reddit.com r/Trading funded trader results",
            "site:reddit.com prop firm challenge success",
            "site:reddit.com funded trader contact information",
            "site:reddit.com forex funded trader email phone",
            "site:reddit.com prop firm trader contact details"
        ]
        
        # Facebook searches
        facebook_queries = [
            "site:facebook.com funded trader group",
            "site:facebook.com forex traders contact",
            "site:facebook.com prop firm traders",
            "site:facebook.com funded trader success",
            "site:facebook.com forex trading contact information"
        ]
        
        # Instagram searches
        instagram_queries = [
            "site:instagram.com funded trader",
            "site:instagram.com prop firm success",
            "site:instagram.com forex trader contact",
            "site:instagram.com funded account trader"
        ]
        
        # General funded trader searches
        general_queries = [
            "funded trader prop firm success contact information",
            "forex funded trader email phone contact details",
            "prop firm challenge success trader contact",
            "funded trading account success story contact",
            "forex prop firm funded trader contact information",
            "funded trader results contact email phone",
            "prop firm evaluation success trader contact",
            "forex funded account trader contact details",
            "funded trader program success contact information",
            "prop firm funded trader contact email phone"
        ]
        
        # Foxex specific searches
        foxex_queries = [
            "Foxex funded trader contact information",
            "Foxex prop firm success trader contact",
            "Foxex funded account trader email phone",
            "Foxex trading funded trader contact details",
            "Foxex prop firm challenge success contact"
        ]
        
        # Prop firm specific searches
        prop_firm_queries = [
            "FTMO funded trader contact information",
            "MyForexFunds funded trader contact",
            "Apex Trader Funding contact information",
            "Topstep funded trader contact details",
            "Earn2Trade funded trader contact",
            "FundedNext funded trader contact information",
            "The5ers funded trader contact details",
            "SurgeTrader funded trader contact",
            "City Traders Imperium funded trader contact",
            "Traders Central funded trader contact information"
        ]
        
        queries.extend(reddit_queries)
        queries.extend(facebook_queries)
        queries.extend(instagram_queries)
        queries.extend(general_queries)
        queries.extend(foxex_queries)
        queries.extend(prop_firm_queries)
        
        return queries
    
    async def _perform_search(
        self, query: str, max_results: int, search_depth: str
    ) -> Dict[str, Any]:
        """Perform search using Tavily API."""
        try:
            response = self.search_client.search(
                query=query,
                search_depth=search_depth,
                max_results=max_results,
                include_answer=False,
                include_raw_content=False,
                include_images=False
            )
            return response
        except Exception as e:
            logger.error(f"Tavily search error: {e}")
            return {"results": []}
    
    def _process_funded_trader_results(
        self, response: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Process and filter funded trader search results."""
        processed_results = []
        
        for result in response.get("results", []):
            try:
                # Extract funded trader information
                trader_info = self._extract_funded_trader_info(result)
                if trader_info:
                    processed_results.append(trader_info)
            except Exception as e:
                logger.warning(f"Error processing funded trader result: {e}")
                continue
        
        return processed_results
    
    def _extract_funded_trader_info(
        self, result: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Extract funded trader information from search result."""
        try:
            title = result.get("title", "")
            content = result.get("content", "")
            url = result.get("url", "")
            
            # Extract trader name
            trader_name = self._extract_trader_name(title, content)
            if not trader_name:
                return None
            
            # Extract contact information
            email, phone = self._extract_contact_info(content)
            
            # Extract platform/prop firm
            platform = self._extract_trading_platform(content)
            
            # Extract funding amount
            funding_amount = self._extract_funding_amount(content)
            
            # Extract social media profiles
            social_profiles = self._extract_social_profiles(content, url)
            
            # Extract location
            location = self._extract_location(content)
            
            # Extract trading performance
            performance = self._extract_trading_performance(content)
            
            return {
                "trader_name": trader_name,
                "email": email,
                "phone": phone,
                "platform": platform,
                "funding_amount": funding_amount,
                "social_profiles": social_profiles,
                "location": location,
                "performance": performance,
                "source_url": url,
                "content_snippet": content[:500] if content else "",
                "lead_type": "funded_trader"
            }
            
        except Exception as e:
            logger.error(f"Error extracting funded trader info: {e}")
            return None
    
    def _extract_trader_name(self, title: str, content: str) -> Optional[str]:
        """Extract trader name from title or content."""
        # Look for patterns like "John Doe", "Trader John", etc.
        name_patterns = [
            r"([A-Z][a-z]+ [A-Z][a-z]+)",  # First Last
            r"Trader ([A-Z][a-z]+)",  # Trader John
            r"([A-Z][a-z]+) is a funded trader",  # John is a funded trader
            r"funded trader ([A-Z][a-z]+)",  # funded trader John
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, title + " " + content)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_trading_platform(self, content: str) -> Optional[str]:
        """Extract trading platform or prop firm name."""
        platforms = [
            "Foxex", "FTMO", "MyForexFunds", "Apex Trader Funding", "Topstep",
            "Earn2Trade", "FundedNext", "The5ers", "SurgeTrader", "City Traders Imperium",
            "Traders Central", "Traders With Edge", "True Forex Funds", "Blue Guardian",
            "Trading Fund", "Traders Global", "Traders Academy", "Traders Central"
        ]
        
        content_lower = content.lower()
        for platform in platforms:
            if platform.lower() in content_lower:
                return platform
        
        return None
    
    def _extract_funding_amount(self, content: str) -> Optional[str]:
        """Extract funding amount from content."""
        amount_patterns = [
            r"\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)k",  # $50k, $100k
            r"\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)K",  # $50K, $100K
            r"(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)k funded",  # 50k funded
            r"(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)K funded",  # 50K funded
            r"funded (\d{1,3}(?:,\d{3})*(?:\.\d{2})?)k",  # funded 50k
            r"funded (\d{1,3}(?:,\d{3})*(?:\.\d{2})?)K",  # funded 50K
        ]
        
        for pattern in amount_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return f"${match.group(1)}k"
        
        return None
    
    def _extract_social_profiles(self, content: str, url: str) -> List[str]:
        """Extract social media profiles."""
        profiles = []
        
        # Look for social media handles
        social_patterns = [
            r"@([a-zA-Z0-9_]+)",  # @username
            r"instagram\.com/([a-zA-Z0-9_]+)",  # instagram.com/username
            r"twitter\.com/([a-zA-Z0-9_]+)",  # twitter.com/username
            r"facebook\.com/([a-zA-Z0-9_]+)",  # facebook.com/username
            r"linkedin\.com/in/([a-zA-Z0-9_]+)",  # linkedin.com/in/username
        ]
        
        for pattern in social_patterns:
            matches = re.findall(pattern, content + " " + url)
            profiles.extend(matches)
        
        return list(set(profiles))
    
    def _extract_trading_performance(self, content: str) -> Optional[str]:
        """Extract trading performance information."""
        performance_patterns = [
            r"(\d+(?:\.\d+)?)% profit",  # 15% profit
            r"(\d+(?:\.\d+)?)% return",  # 15% return
            r"(\d+(?:\.\d+)?)% gain",  # 15% gain
            r"profit (\d+(?:\.\d+)?)%",  # profit 15%
            r"return (\d+(?:\.\d+)?)%",  # return 15%
        ]
        
        for pattern in performance_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return f"{match.group(1)}%"
        
        return None
    
    def _extract_contact_info(self, content: str) -> tuple[Optional[str], Optional[str]]:
        """Extract email and phone from content."""
        # Email pattern
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        email_match = re.search(email_pattern, content)
        email = email_match.group(0) if email_match else None
        
        # Phone pattern
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # ************
            r'\b\(\d{3}\)\s*\d{3}[-.]?\d{4}\b',  # (*************
            r'\+\d{1,3}\s*\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # +1 ************
        ]
        
        phone = None
        for pattern in phone_patterns:
            phone_match = re.search(pattern, content)
            if phone_match:
                phone = phone_match.group(0)
                break
        
        return email, phone
    
    def _extract_location(self, content: str) -> Optional[str]:
        """Extract location from content."""
        # Common location patterns
        location_patterns = [
            r"from ([A-Z][a-z]+(?: [A-Z][a-z]+)*)",  # from New York
            r"in ([A-Z][a-z]+(?: [A-Z][a-z]+)*)",  # in Los Angeles
            r"based in ([A-Z][a-z]+(?: [A-Z][a-z]+)*)",  # based in Chicago
            r"located in ([A-Z][a-z]+(?: [A-Z][a-z]+)*)",  # located in Miami
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1)
        
        return None
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate results based on trader name and email."""
        seen = set()
        unique_results = []
        
        for result in results:
            trader_name = result.get("trader_name", "")
            email = result.get("email", "")
            
            # Create unique identifier
            identifier = f"{trader_name.lower()}_{email.lower()}"
            
            if identifier not in seen and trader_name:
                seen.add(identifier)
                unique_results.append(result)
        
        return unique_results
    
    async def qualify_funded_trader(
        self, 
        trader_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Qualify a funded trader using specialized criteria.
        
        Args:
            trader_data: Funded trader information
        
        Returns:
            Qualification results with score and reasoning
        """
        try:
            # Create specialized qualification prompt
            prompt = self._create_funded_trader_qualification_prompt(trader_data)
            
            # Get LLM response
            response = await self._get_qualification_response(prompt)
            
            # Parse qualification results
            qualification_result = self._parse_qualification_response(response)
            
            # Add metadata
            qualification_result.update({
                "trader_name": trader_data.get("trader_name"),
                "platform": trader_data.get("platform"),
                "funding_amount": trader_data.get("funding_amount"),
                "qualification_threshold": self.qualification_threshold,
                "is_qualified": qualification_result.get("score", 0) >= self.qualification_threshold
            })
            
            return qualification_result
            
        except Exception as e:
            logger.error(f"Error qualifying funded trader {trader_data.get('trader_name', 'Unknown')}: {e}")
            return self._create_fallback_qualification(trader_data)
    
    def _create_funded_trader_qualification_prompt(
        self, 
        trader_data: Dict[str, Any]
    ) -> str:
        """Create specialized qualification prompt for funded traders."""
        
        prompt_template = f"""
You are an expert lead qualification specialist for funded traders and prop firm traders. Your task is to evaluate a potential funded trader lead and provide a qualification score from 0.0 to 1.0, where 1.0 is the highest quality lead.

Trader Information:
- Trader Name: {trader_data.get('trader_name', 'Unknown')}
- Platform/Prop Firm: {trader_data.get('platform', 'Unknown')}
- Funding Amount: {trader_data.get('funding_amount', 'Unknown')}
- Email: {trader_data.get('email', 'Unknown')}
- Phone: {trader_data.get('phone', 'Unknown')}
- Location: {trader_data.get('location', 'Unknown')}
- Social Profiles: {trader_data.get('social_profiles', [])}
- Performance: {trader_data.get('performance', 'Unknown')}
- Source URL: {trader_data.get('source_url', 'Unknown')}
- Content Snippet: {trader_data.get('content_snippet', 'Unknown')}

Qualification Criteria for Funded Traders:
1. **Contact Information Quality** (30%):
   - Valid email address
   - Phone number present
   - Social media profiles available
   - Professional contact details

2. **Trading Platform/Prop Firm** (25%):
   - Reputable prop firm (FTMO, MyForexFunds, Apex, etc.)
   - Foxex platform usage
   - Established trading platform
   - Verified prop firm success

3. **Funding Amount** (20%):
   - Higher funding amounts preferred ($50k+)
   - Multiple funded accounts
   - Consistent funding history
   - Professional funding levels

4. **Performance Indicators** (15%):
   - Profit percentages mentioned
   - Trading success stories
   - Consistent performance
   - Risk management evidence

5. **Professional Presence** (10%):
   - Social media activity
   - Trading community engagement
   - Professional online presence
   - Educational content sharing

Evaluation Guidelines:
1. Score from 0.0 to 1.0 (1.0 = highest quality)
2. Prioritize traders with complete contact information
3. Value traders on established prop firms
4. Consider funding amounts and performance
5. Look for professional online presence

Please provide your evaluation in the following JSON format:
{{
    "score": 0.85,
    "reasoning": "Detailed explanation of the score",
    "strengths": ["List of positive factors"],
    "weaknesses": ["List of concerns or missing information"],
    "recommendations": ["Suggestions for follow-up or additional research"],
    "confidence": 0.9,
    "key_factors": ["Most important factors that influenced the score"],
    "contact_quality": "High/Medium/Low",
    "platform_reputation": "High/Medium/Low",
    "funding_level": "High/Medium/Low"
}}

Focus on practical business value and potential for successful engagement with funded traders.
"""
        
        return prompt_template
    
    async def _get_qualification_response(self, prompt: str) -> str:
        """Get qualification response from LLM."""
        try:
            response = await self.llm.ainvoke(prompt)
            return response.content
        except Exception as e:
            logger.error(f"Error getting LLM response: {e}")
            return "{}"
    
    def _parse_qualification_response(self, response: str) -> Dict[str, Any]:
        """Parse qualification response from LLM."""
        try:
            # Try to extract JSON from response
            import json
            
            # Find JSON in response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                result = json.loads(json_str)
                return result
            else:
                # Fallback parsing
                return self._fallback_parse_response(response)
                
        except Exception as e:
            logger.error(f"Error parsing qualification response: {e}")
            return self._fallback_parse_response(response)
    
    def _fallback_parse_response(self, response: str) -> Dict[str, Any]:
        """Fallback parsing for qualification response."""
        # Extract score if present
        score_match = re.search(r'"score":\s*(\d+\.?\d*)', response)
        score = float(score_match.group(1)) if score_match else 0.5
        
        return {
            "score": score,
            "reasoning": "Fallback qualification due to parsing error",
            "strengths": ["Contact information available"],
            "weaknesses": ["Limited information for detailed analysis"],
            "recommendations": ["Verify contact information and reach out"],
            "confidence": 0.5,
            "key_factors": ["Basic contact information"],
            "contact_quality": "Medium",
            "platform_reputation": "Unknown",
            "funding_level": "Unknown"
        }
    
    def _create_fallback_qualification(
        self, 
        trader_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create fallback qualification when LLM fails."""
        return {
            "score": 0.5,
            "reasoning": "Fallback qualification - LLM unavailable",
            "strengths": ["Basic trader information available"],
            "weaknesses": ["Limited qualification data"],
            "recommendations": ["Manual review recommended"],
            "confidence": 0.3,
            "key_factors": ["Basic contact information"],
            "contact_quality": "Medium",
            "platform_reputation": "Unknown",
            "funding_level": "Unknown",
            "trader_name": trader_data.get("trader_name"),
            "platform": trader_data.get("platform"),
            "funding_amount": trader_data.get("funding_amount"),
            "qualification_threshold": self.qualification_threshold,
            "is_qualified": False
        }
    
    async def batch_qualify_funded_traders(
        self, 
        traders: List[Dict[str, Any]],
        batch_size: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Qualify multiple funded traders in batches.
        
        Args:
            traders: List of trader data
            batch_size: Number of traders to qualify concurrently
        
        Returns:
            List of qualification results
        """
        results = []
        
        for i in range(0, len(traders), batch_size):
            batch = traders[i:i + batch_size]
            
            # Qualify batch concurrently
            tasks = [self.qualify_funded_trader(trader) for trader in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter out exceptions
            valid_results = [r for r in batch_results if not isinstance(r, Exception)]
            results.extend(valid_results)
            
            # Add delay between batches
            if i + batch_size < len(traders):
                await asyncio.sleep(1)
        
        return results
    
    def get_qualification_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get summary of funded trader qualification results."""
        if not results:
            return {"total": 0, "qualified": 0, "average_score": 0}
        
        qualified_count = sum(1 for r in results if r.get("is_qualified", False))
        scores = [r.get("score", 0) for r in results if r.get("score")]
        average_score = sum(scores) / len(scores) if scores else 0
        
        platforms = {}
        funding_levels = {}
        
        for result in results:
            platform = result.get("platform_reputation", "Unknown")
            platforms[platform] = platforms.get(platform, 0) + 1
            
            funding = result.get("funding_level", "Unknown")
            funding_levels[funding] = funding_levels.get(funding, 0) + 1
        
        return {
            "total": len(results),
            "qualified": qualified_count,
            "qualified_percentage": (qualified_count / len(results)) * 100 if results else 0,
            "average_score": average_score,
            "platform_distribution": platforms,
            "funding_level_distribution": funding_levels
        } 