"""Simple lead generation workflow."""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from models import WorkflowState, Industry, LeadStatus, LeadSource
from services.search_service import SearchService
from services.contact_extraction_service import ContactExtractionService
from services.qualification_service import QualificationService
from services.contact_extraction_service import ContactExtractionService
from database import LeadRepository, SearchSessionRepository
from config import settings

logger = logging.getLogger(__name__)


class SimpleLeadGenerationWorkflow:
    """Simple workflow for lead generation focused on contact information."""
    
    def __init__(self):
        self.search_service = SearchService()
        self.contact_extraction_service = ContactExtractionService()
        self.qualification_service = QualificationService()
        self.contact_extraction_service = ContactExtractionService()
    
    async def run_workflow(
        self, 
        query: str, 
        industry: Industry,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Run the simple lead generation workflow.
        
        Args:
            query: Search query
            industry: Target industry
            config: Optional configuration
        
        Returns:
            Workflow results
        """
        try:
            logger.info(f"Starting simple lead generation workflow for: {query} in {industry.value}")
            
            # Initialize workflow state
            state = WorkflowState(
                query=query,
                industry=industry,
                metadata={
                    "workflow_started": datetime.utcnow().isoformat(),
                    "config": config or {}
                }
            )
            
            # Step 1: Search for leads
            await self._search_leads(state)
            
            # Step 2: Extract contact information
            await self._extract_contact_info(state)
            
            # Step 3: Qualify leads
            await self._qualify_leads(state)
            
            # Step 4: Store leads (skip for testing)
            if state.qualified_leads and not state.errors:
                logger.info(f"Would store {len(state.qualified_leads)} qualified leads")
                state.stored_leads = list(range(len(state.qualified_leads)))  # Mock storage
            
            # Prepare results
            results = {
                "query": state.query,
                "industry": state.industry.value,
                "search_results_count": len(state.search_results),
                "leads_with_contacts_count": len([r for r in state.search_results if r.get("email") or r.get("phone")]),
                "qualified_leads_count": len(state.qualified_leads),
                "stored_leads_count": len(state.stored_leads),
                "errors": state.errors,
                "metadata": state.metadata,
                "success": len(state.errors) == 0
            }
            
            logger.info(f"Simple workflow completed. Results: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Error in simple workflow: {e}")
            return {
                "query": query,
                "industry": industry.value,
                "search_results_count": 0,
                "leads_with_contacts_count": 0,
                "qualified_leads_count": 0,
                "stored_leads_count": 0,
                "errors": [str(e)],
                "metadata": {},
                "success": False
            }
    
    async def _search_leads(self, state: WorkflowState):
        """Search for leads with focus on contact information."""
        try:
            logger.info(f"Starting lead search for query: {state.query}")
            
            # Search for leads
            search_results = await self.search_service.search_leads(
                query=state.query,
                industry=state.industry.value,
                max_results=settings.max_leads_per_search
            )
            
            # Prioritize leads with contact information
            leads_with_contacts = [r for r in search_results if r.get("has_contact_info")]
            leads_without_contacts = [r for r in search_results if not r.get("has_contact_info")]
            
            # Sort by contact information availability
            state.search_results = leads_with_contacts + leads_without_contacts
            
            state.metadata["search_completed"] = datetime.utcnow().isoformat()
            state.metadata["search_results_count"] = len(search_results)
            state.metadata["leads_with_contacts"] = len(leads_with_contacts)
            
            logger.info(f"Search completed. Found {len(search_results)} potential leads ({len(leads_with_contacts)} with contacts)")
            
        except Exception as e:
            logger.error(f"Error in search_leads: {e}")
            state.errors.append(f"Search error: {str(e)}")
    
    async def _extract_contact_info(self, state: WorkflowState):
        """Extract contact information from leads."""
        try:
            logger.info("Starting contact information extraction")
            
            if not state.search_results:
                logger.warning("No search results to extract contacts from")
                return
            
            # Extract contact information from all leads
            leads_with_contacts = await self.contact_extraction_service.extract_contacts_from_multiple_sources(
                state.search_results
            )
            
            # Update search results with extracted contact information
            state.search_results = leads_with_contacts
            
            # Count leads with contact information
            leads_with_emails = [r for r in leads_with_contacts if r.get("email")]
            leads_with_phones = [r for r in leads_with_contacts if r.get("phone")]
            
            state.metadata["contact_extraction_completed"] = datetime.utcnow().isoformat()
            state.metadata["leads_with_emails"] = len(leads_with_emails)
            state.metadata["leads_with_phones"] = len(leads_with_phones)
            
            logger.info(f"Contact extraction completed. {len(leads_with_emails)} leads with emails, {len(leads_with_phones)} with phones")
            
        except Exception as e:
            logger.error(f"Error in extract_contact_info: {e}")
            state.errors.append(f"Contact extraction error: {str(e)}")
    
    async def _qualify_leads(self, state: WorkflowState):
        """Qualify leads using LLM or fallback methods."""
        try:
            logger.info("Starting lead qualification")
            
            if not state.search_results:
                logger.warning("No leads to qualify")
                return
            
            # Qualify leads
            qualification_results = await self.qualification_service.batch_qualify_leads(
                state.search_results,
                state.industry.value,
                batch_size=10
            )
            
            # Filter qualified leads
            qualified_leads = []
            for i, lead in enumerate(state.search_results):
                if i < len(qualification_results):
                    qualification = qualification_results[i]
                    if qualification.get("is_qualified", False):
                        # Merge lead data with qualification results
                        qualified_lead = {**lead, **qualification}
                        qualified_leads.append(qualified_lead)
            
            # Update state
            state.qualified_leads = qualified_leads
            state.metadata["qualification_completed"] = datetime.utcnow().isoformat()
            state.metadata["qualified_leads_count"] = len(qualified_leads)
            
            logger.info(f"Qualification completed. {len(qualified_leads)} leads qualified")
            
        except Exception as e:
            logger.error(f"Error in qualify_leads: {e}")
            state.errors.append(f"Qualification error: {str(e)}")
    
    async def _store_leads(self, state: WorkflowState):
        """Store qualified leads in database."""
        try:
            logger.info("Starting lead storage")
            
            if not state.qualified_leads:
                logger.warning("No qualified leads to store")
                return
            
            # Store leads in database
            from database import get_db_session
            
            async for db_session in get_db_session():
                session_repo = SearchSessionRepository(db_session)
                lead_repo = LeadRepository(db_session)
                
                # Create search session
                session_data = {
                    "query": state.query,
                    "industry": state.industry.value,
                    "leads_found": len(state.search_results),
                    "leads_qualified": len(state.qualified_leads),
                    "status": "completed",
                    "completed_at": datetime.utcnow(),
                    "metadata": state.metadata
                }
                
                search_session = await session_repo.create_session(session_data)
                
                # Store qualified leads
                stored_lead_ids = []
                for lead_data in state.qualified_leads:
                    try:
                        # Add search session ID to lead data
                        lead_data["search_session_id"] = search_session.id
                        
                        # Prepare lead data for storage
                        lead_create_data = self._prepare_lead_for_storage(lead_data)
                        
                        # Create lead
                        lead = await lead_repo.create_lead(lead_create_data)
                        stored_lead_ids.append(lead.id)
                        
                    except Exception as e:
                        logger.error(f"Error storing lead {lead_data.get('company_name', 'Unknown')}: {e}")
                        state.errors.append(f"Storage error for {lead_data.get('company_name', 'Unknown')}: {str(e)}")
                
                # Update state
                state.stored_leads = stored_lead_ids
                state.metadata["storage_completed"] = datetime.utcnow().isoformat()
                state.metadata["stored_leads_count"] = len(stored_lead_ids)
                state.metadata["search_session_id"] = search_session.id
                
                logger.info(f"Storage completed. Stored {len(stored_lead_ids)} leads")
                break
                
        except Exception as e:
            logger.error(f"Error in store_leads: {e}")
            state.errors.append(f"Storage error: {str(e)}")
    
    def _prepare_lead_for_storage(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare lead data for database storage."""
        
        return {
            "company_name": lead_data.get("company_name", ""),
            "website": lead_data.get("website"),
            "email": lead_data.get("email"),
            "phone": lead_data.get("phone"),
            "industry": lead_data.get("industry", ""),
            "services": lead_data.get("services"),
            "description": lead_data.get("description"),
            "location": lead_data.get("location"),
            "employee_count": lead_data.get("employee_count"),
            "revenue_range": lead_data.get("revenue_range"),
            "status": LeadStatus.QUALIFIED.value if lead_data.get("is_qualified") else LeadStatus.DISCOVERED.value,
            "qualification_score": lead_data.get("score"),
            "qualification_reason": lead_data.get("reasoning"),
            "source": LeadSource.WEB_SEARCH.value,
            "source_url": lead_data.get("source_url"),
            "search_query": lead_data.get("search_query"),
            "search_session_id": lead_data.get("search_session_id"),
            "raw_data": {
                "search_data": lead_data.get("raw_data", {}),
                "contact_data": {
                    "all_emails": lead_data.get("all_emails", []),
                    "all_phones": lead_data.get("all_phones", []),
                    "contact_extraction_metadata": lead_data.get("contact_extraction_metadata", {})
                },
                "qualification_data": {
                    "strengths": lead_data.get("strengths", []),
                    "weaknesses": lead_data.get("weaknesses", []),
                    "recommendations": lead_data.get("recommendations", []),
                    "key_factors": lead_data.get("key_factors", [])
                }
            },
            "notes": f"Qualified with score: {lead_data.get('score', 0)}. Contact info: Email={bool(lead_data.get('email'))}, Phone={bool(lead_data.get('phone'))}"
        }
