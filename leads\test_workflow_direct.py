"""Direct test of the workflow to verify contact extraction works."""

import asyncio
import logging
from workflow.simple_workflow import SimpleLeadGenerationWorkflow
from models import Industry

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_workflow():
    """Test the workflow directly."""
    
    try:
        workflow = SimpleLeadGenerationWorkflow()
        
        print("Testing workflow with forex query...")
        
        # Test workflow
        results = await workflow.run_workflow(
            query="funded trading programs forex traders",
            industry=Industry.FOREX,
            config={"max_results": 10}
        )
        
        print(f"\nWorkflow completed!")
        print(f"Success: {results['success']}")
        print(f"Search results count: {results['search_results_count']}")
        print(f"Leads with contacts count: {results.get('leads_with_contacts_count', 0)}")
        print(f"Qualified leads count: {results['qualified_leads_count']}")
        print(f"Stored leads count: {results['stored_leads_count']}")
        
        if results['errors']:
            print(f"\nErrors:")
            for error in results['errors']:
                print(f"  - {error}")
        
        print(f"\nMetadata:")
        for key, value in results['metadata'].items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"Error testing workflow: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_workflow())
