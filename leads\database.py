"""Database connection and session management."""

import asyncio
from typing import As<PERSON><PERSON>enerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool

from config import settings
from models import Base


class DatabaseManager:
    """Database connection manager."""
    
    def __init__(self):
        self.engine = None
        self.async_session_maker = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize database connection."""
        if self._initialized:
            return
        
        # Ensure database URL uses async driver and handle SSL parameters
        database_url = settings.database_url
        if database_url.startswith("postgresql://"):
            database_url = database_url.replace("postgresql://", "postgresql+asyncpg://")
        elif database_url.startswith("postgres://"):
            database_url = database_url.replace("postgres://", "postgresql+asyncpg://")
        
        # Remove parameters that are not compatible with asyncpg
        if "sslmode=" in database_url or "channel_binding=" in database_url:
            # Parse URL and remove incompatible parameters
            from urllib.parse import urlparse, parse_qs, urlencode
            
            parsed = urlparse(database_url)
            query_params = parse_qs(parsed.query)
            
            # Remove parameters not compatible with asyncpg
            incompatible_params = ['sslmode', 'channel_binding', 'gssencmode']
            for param in incompatible_params:
                if param in query_params:
                    del query_params[param]
            
            # Rebuild URL without incompatible parameters
            new_query = urlencode(query_params, doseq=True)
            database_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            if new_query:
                database_url += f"?{new_query}"
        
        # Create async engine
        self.engine = create_async_engine(
            database_url,
            echo=settings.debug,
            poolclass=NullPool,
            pool_pre_ping=True,
            pool_recycle=300,
        )
        
        # Create async session maker
        self.async_session_maker = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )
        
        # Create tables
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        self._initialized = True
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session."""
        if not self._initialized:
            await self.initialize()
        
        async with self.async_session_maker() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def close(self):
        """Close database connections."""
        if self.engine:
            await self.engine.dispose()


# Global database manager instance
db_manager = DatabaseManager()


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Dependency for FastAPI to get database session."""
    async for session in db_manager.get_session():
        yield session


class LeadRepository:
    """Repository for lead operations."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create_lead(self, lead_data: dict) -> "Lead":
        """Create a new lead."""
        from models import Lead
        
        lead = Lead(**lead_data)
        self.session.add(lead)
        await self.session.commit()
        await self.session.refresh(lead)
        return lead
    
    async def get_lead(self, lead_id: int) -> "Lead":
        """Get lead by ID."""
        from models import Lead
        from sqlalchemy import select
        
        result = await self.session.execute(
            select(Lead).where(Lead.id == lead_id)
        )
        return result.scalar_one_or_none()
    
    async def get_leads(
        self,
        skip: int = 0,
        limit: int = 100,
        status: str = None,
        industry: str = None,
    ) -> list["Lead"]:
        """Get leads with filters."""
        from models import Lead
        from sqlalchemy import select
        
        query = select(Lead)
        
        if status:
            query = query.where(Lead.status == status)
        if industry:
            query = query.where(Lead.industry == industry)
        
        query = query.offset(skip).limit(limit)
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def update_lead(self, lead_id: int, update_data: dict) -> "Lead":
        """Update a lead."""
        from models import Lead
        from sqlalchemy import select, update
        
        # Update the lead
        await self.session.execute(
            update(Lead)
            .where(Lead.id == lead_id)
            .values(**update_data)
        )
        await self.session.commit()
        
        # Return updated lead
        result = await self.session.execute(
            select(Lead).where(Lead.id == lead_id)
        )
        return result.scalar_one_or_none()
    
    async def delete_lead(self, lead_id: int) -> bool:
        """Delete a lead."""
        from models import Lead
        from sqlalchemy import select, delete
        
        # Check if lead exists
        result = await self.session.execute(
            select(Lead).where(Lead.id == lead_id)
        )
        lead = result.scalar_one_or_none()
        
        if not lead:
            return False
        
        # Delete the lead
        await self.session.execute(
            delete(Lead).where(Lead.id == lead_id)
        )
        await self.session.commit()
        return True
    
    async def get_leads_by_industry(self, industry: str, limit: int = 50) -> list["Lead"]:
        """Get leads by industry."""
        from models import Lead
        from sqlalchemy import select
        
        result = await self.session.execute(
            select(Lead)
            .where(Lead.industry == industry)
            .order_by(Lead.discovered_at.desc())
            .limit(limit)
        )
        return result.scalars().all()
    
    async def get_qualified_leads(self, threshold: float = 0.7) -> list["Lead"]:
        """Get leads above qualification threshold."""
        from models import Lead
        from sqlalchemy import select
        
        result = await self.session.execute(
            select(Lead)
            .where(Lead.qualification_score >= threshold)
            .order_by(Lead.qualification_score.desc())
        )
        return result.scalars().all()
    
    async def get_leads_by_session(self, session_id: int) -> list["Lead"]:
        """Get leads by search session ID."""
        from models import Lead
        from sqlalchemy import select
        
        result = await self.session.execute(
            select(Lead)
            .where(Lead.search_session_id == session_id)
            .order_by(Lead.discovered_at.desc())
        )
        return result.scalars().all()


class SearchSessionRepository:
    """Repository for search session operations."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def create_session(self, session_data: dict) -> "SearchSession":
        """Create a new search session."""
        from models import SearchSession
        
        session = SearchSession(**session_data)
        self.session.add(session)
        await self.session.commit()
        await self.session.refresh(session)
        return session
    
    async def update_session(self, session_id: int, update_data: dict) -> "SearchSession":
        """Update a search session."""
        from models import SearchSession
        from sqlalchemy import select, update
        
        await self.session.execute(
            update(SearchSession)
            .where(SearchSession.id == session_id)
            .values(**update_data)
        )
        await self.session.commit()
        
        result = await self.session.execute(
            select(SearchSession).where(SearchSession.id == session_id)
        )
        return result.scalar_one_or_none()
    
    async def get_session(self, session_id: int) -> "SearchSession":
        """Get search session by ID."""
        from models import SearchSession
        from sqlalchemy import select
        
        result = await self.session.execute(
            select(SearchSession).where(SearchSession.id == session_id)
        )
        return result.scalar_one_or_none() 