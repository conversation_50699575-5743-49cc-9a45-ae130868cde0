[project]
name = "leads"
version = "0.1.0"
description = "AI-powered lead generation agent for forex traders and software houses"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "langgraph>=0.3.0",
    "langchain>=0.3.0",
    "langchain-google-genai>=2.0.0",
    "fastapi>=0.110.0",
    "uvicorn>=0.27.0",
    "pydantic>=2.6.0",
    "tavily-python>=0.3.0",
    "scrapegraphai>=0.1.0",
    "asyncpg>=0.29.0",
    "sqlalchemy>=2.0.25",
    "alembic>=1.13.0",
    "python-dotenv>=1.0.0",
    "httpx>=0.27.0",
    "beautifulsoup4>=4.12.0",
    "aiofiles>=23.2.0",
    "python-multipart>=0.0.9",
    "pydantic-settings>=2.2.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.ruff]
line-length = 88
target-version = "py310"

[tool.ruff.lint]
select = ["E", "F", "I", "N", "W", "B", "C4", "UP"]
ignore = ["E501", "B008"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
