"""Data models for the Lead Generation AI Agent."""

from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, JSO<PERSON>
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class LeadStatus(str, Enum):
    """Status of a lead in the pipeline."""
    DISCOVERED = "discovered"
    QUALIFIED = "qualified"
    CONTACTED = "contacted"
    CONVERTED = "converted"
    REJECTED = "rejected"


class Industry(str, Enum):
    """Target industries for lead generation."""
    HEALTHCARE = "healthcare"
    FINANCE = "finance"
    ECOMMERCE = "e-commerce"
    FOREX = "forex"
    SOFTWARE = "software"
    OTHER = "other"


class LeadSource(str, Enum):
    """Source of the lead."""
    WEB_SEARCH = "web_search"
    SOCIAL_MEDIA = "social_media"
    DIRECTORY = "directory"
    REFERRAL = "referral"


# SQLAlchemy Models
class Lead(Base):
    """Database model for leads."""
    __tablename__ = "leads"
    
    id = Column(Integer, primary_key=True, index=True)
    company_name = Column(String(255), nullable=False, index=True)
    website = Column(String(500), nullable=True)
    email = Column(String(255), nullable=True, index=True)
    phone = Column(String(50), nullable=True)
    industry = Column(String(100), nullable=False, index=True)
    services = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    location = Column(String(255), nullable=True)
    employee_count = Column(String(100), nullable=True)
    revenue_range = Column(String(100), nullable=True)
    
    # Lead qualification
    status = Column(String(50), default=LeadStatus.DISCOVERED.value, index=True)
    qualification_score = Column(Float, nullable=True)
    qualification_reason = Column(Text, nullable=True)
    
    # Source and metadata
    source = Column(String(50), default=LeadSource.WEB_SEARCH.value)
    source_url = Column(String(1000), nullable=True)
    search_query = Column(String(500), nullable=True)
    search_session_id = Column(Integer, nullable=True, index=True)
    
    # Timestamps
    discovered_at = Column(DateTime, default=datetime.utcnow)
    qualified_at = Column(DateTime, nullable=True)
    contacted_at = Column(DateTime, nullable=True)
    converted_at = Column(DateTime, nullable=True)
    
    # Additional data
    raw_data = Column(JSON, nullable=True)
    notes = Column(Text, nullable=True)
    
    # GDPR compliance
    gdpr_consent = Column(Boolean, default=False)
    data_retention_until = Column(DateTime, nullable=True)


class SearchSession(Base):
    """Database model for search sessions."""
    __tablename__ = "search_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    query = Column(String(500), nullable=False)
    industry = Column(String(100), nullable=False)
    leads_found = Column(Integer, default=0)
    leads_qualified = Column(Integer, default=0)
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    status = Column(String(50), default="running")
    session_metadata = Column(JSON, nullable=True)


# Pydantic Models for API
class LeadBase(BaseModel):
    """Base lead model."""
    company_name: str = Field(..., description="Name of the company")
    website: Optional[str] = Field(None, description="Company website")
    email: Optional[str] = Field(None, description="Contact email")
    phone: Optional[str] = Field(None, description="Contact phone")
    industry: Industry = Field(..., description="Industry category")
    services: Optional[str] = Field(None, description="Services offered")
    description: Optional[str] = Field(None, description="Company description")
    location: Optional[str] = Field(None, description="Company location")
    employee_count: Optional[str] = Field(None, description="Number of employees")
    revenue_range: Optional[str] = Field(None, description="Revenue range")


class LeadCreate(LeadBase):
    """Model for creating a new lead."""
    source: LeadSource = Field(default=LeadSource.WEB_SEARCH)
    source_url: Optional[str] = Field(None, description="Source URL")
    search_query: Optional[str] = Field(None, description="Search query that found this lead")
    raw_data: Optional[Dict[str, Any]] = Field(None, description="Raw scraped data")


class LeadUpdate(BaseModel):
    """Model for updating a lead."""
    status: Optional[LeadStatus] = None
    qualification_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    qualification_reason: Optional[str] = None
    notes: Optional[str] = None
    contacted_at: Optional[datetime] = None
    converted_at: Optional[datetime] = None


class LeadResponse(LeadBase):
    """Model for lead response."""
    id: int
    status: LeadStatus
    qualification_score: Optional[float] = None
    qualification_reason: Optional[str] = None
    source: LeadSource
    source_url: Optional[str] = None
    search_query: Optional[str] = None
    discovered_at: datetime
    qualified_at: Optional[datetime] = None
    contacted_at: Optional[datetime] = None
    converted_at: Optional[datetime] = None
    notes: Optional[str] = None
    
    class Config:
        from_attributes = True


class SearchRequest(BaseModel):
    """Model for search requests."""
    query: str = Field(..., description="Search query")
    industry: Industry = Field(..., description="Target industry")
    max_results: Optional[int] = Field(50, description="Maximum number of results")
    include_contact_info: bool = Field(True, description="Whether to extract contact information")


class SearchResponse(BaseModel):
    """Model for search response."""
    session_id: int
    query: str
    industry: Industry
    total_leads_found: int
    qualified_leads: int
    status: str
    leads: List[LeadResponse]
    search_metadata: Dict[str, Any]


class LeadQualificationRequest(BaseModel):
    """Model for lead qualification requests."""
    lead_id: int
    qualification_score: float = Field(..., ge=0.0, le=1.0)
    qualification_reason: str = Field(..., description="Reason for qualification score")
    notes: Optional[str] = None


class WorkflowState(BaseModel):
    """State model for LangGraph workflow."""
    query: str
    industry: Industry
    search_results: List[Dict[str, Any]] = Field(default_factory=list)
    scraped_leads: List[Dict[str, Any]] = Field(default_factory=list)
    qualified_leads: List[Dict[str, Any]] = Field(default_factory=list)
    stored_leads: List[int] = Field(default_factory=list)
    errors: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict) 