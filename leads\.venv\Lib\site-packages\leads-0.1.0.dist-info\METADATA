Metadata-Version: 2.4
Name: leads
Version: 0.1.0
Summary: AI-powered lead generation agent for forex traders and software houses
Requires-Python: >=3.10
Requires-Dist: aiofiles>=23.2.0
Requires-Dist: alembic>=1.13.0
Requires-Dist: asyncpg>=0.29.0
Requires-Dist: beautifulsoup4>=4.12.0
Requires-Dist: fastapi>=0.110.0
Requires-Dist: httpx>=0.27.0
Requires-Dist: langchain-google-genai>=2.0.0
Requires-Dist: langchain-groq>=0.1.0
Requires-Dist: langchain>=0.3.0
Requires-Dist: langgraph>=0.3.0
Requires-Dist: pydantic-settings>=2.2.0
Requires-Dist: pydantic>=2.6.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: python-multipart>=0.0.9
Requires-Dist: scrapegraphai>=0.1.0
Requires-Dist: sqlalchemy>=2.0.25
Requires-Dist: tavily-python>=0.3.0
Requires-Dist: uvicorn>=0.27.0
Description-Content-Type: text/markdown

# Lead Generation AI Agent

An intelligent AI-powered lead generation system designed for forex traders and software houses targeting industries like healthcare, finance, and e-commerce. Built with LangGraph, Gemini LLM, and modern web technologies.

## 🚀 Features

- **Intelligent Search**: Uses Tavily for real-time web search to discover potential leads
- **Data Extraction**: Leverages ScrapeGraphAI for structured data extraction from company websites
- **AI Qualification**: Employs Gemini LLM for intelligent lead scoring and qualification
- **Graph-based Workflow**: LangGraph orchestrates the entire lead generation process
- **Persistent Storage**: Neon PostgreSQL for reliable data storage
- **RESTful API**: FastAPI provides comprehensive endpoints for lead management
- **Scalable Architecture**: Modular design ensures easy maintenance and scaling
- **GDPR Compliance**: Built-in data privacy and retention controls

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI API   │    │  LangGraph      │    │   Database      │
│   (REST/HTTP)   │◄──►│  Workflow       │◄──►│   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Search        │    │   Scraping      │    │   Qualification │
│   Service       │    │   Service       │    │   Service       │
│   (Tavily)      │    │   (ScrapeGraph) │    │   (Gemini LLM)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Prerequisites

- Python 3.10+
- PostgreSQL database (Neon recommended)
- API keys for:
  - Google Gemini
  - Tavily Search
  - ScrapeGraphAI

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd leads
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -e .
   ```

4. **Set up environment variables**
   Create a `.env` file in the project root:
   ```env
   # API Keys
   GOOGLE_API_KEY=your_google_api_key_here
   TAVILY_API_KEY=your_tavily_api_key_here
   SCRAPEGRAPH_API_KEY=your_scrapegraph_api_key_here
   
   # Database Configuration
   DATABASE_URL=postgresql+asyncpg://username:password@host:port/database_name
   NEON_DATABASE_URL=postgresql+asyncpg://username:password@host:port/database_name
   
   # Application Settings
   APP_NAME=Lead Generation AI Agent
   DEBUG=true
   LOG_LEVEL=INFO
   
   # Lead Generation Settings
   MAX_LEADS_PER_SEARCH=50
   LEAD_QUALIFICATION_THRESHOLD=0.7
   SEARCH_DELAY_SECONDS=2
   
   # Target Industries
   TARGET_INDUSTRIES=healthcare,finance,e-commerce,forex,software
   
   # Compliance Settings
   GDPR_COMPLIANT=true
   DATA_RETENTION_DAYS=90
   ```

## 🚀 Quick Start

### Running the API Server

```bash
# Start the FastAPI server
python -m uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
```

The API will be available at `http://localhost:8000`

### API Documentation

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## 📚 API Usage

### 1. Search for Leads

```bash
curl -X POST "http://localhost:8000/api/v1/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "healthcare software companies",
    "industry": "healthcare",
    "max_results": 50,
    "include_contact_info": true
  }'
```

### 2. Check Search Status

```bash
curl "http://localhost:8000/api/v1/search/1"
```

### 3. Get All Leads

```bash
curl "http://localhost:8000/api/v1/leads?limit=10&industry=healthcare"
```

### 4. Update Lead Status

```bash
curl -X PUT "http://localhost:8000/api/v1/leads/1" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "contacted",
    "notes": "Initial contact made"
  }'
```

### 5. Get Analytics

```bash
curl "http://localhost:8000/api/v1/analytics/summary"
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_API_KEY` | Google Gemini API key | Required |
| `TAVILY_API_KEY` | Tavily Search API key | Required |
| `SCRAPEGRAPH_API_KEY` | ScrapeGraphAI API key | Required |
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `MAX_LEADS_PER_SEARCH` | Maximum leads per search | 50 |
| `LEAD_QUALIFICATION_THRESHOLD` | Minimum qualification score | 0.7 |
| `SEARCH_DELAY_SECONDS` | Delay between searches | 2 |

### Supported Industries

- `healthcare`: Healthcare software and technology
- `finance`: Financial technology and banking
- `e-commerce`: E-commerce and online retail
- `forex`: Forex trading and currency exchange
- `software`: Software development and technology

## 🔄 Workflow Process

1. **Search Phase**: Uses Tavily to search for potential leads
2. **Scraping Phase**: Extracts contact and business information from websites
3. **Qualification Phase**: Uses Gemini LLM to score and qualify leads
4. **Storage Phase**: Stores qualified leads in PostgreSQL database

## 📊 Data Models

### Lead Status
- `discovered`: Initial discovery
- `qualified`: AI-qualified lead
- `contacted`: Human contact made
- `converted`: Successfully converted
- `rejected`: Not suitable

### Lead Information
- Company name, website, email, phone
- Industry classification
- Services and description
- Location and company size
- Qualification score and reasoning
- Source and metadata

## 🛡️ Security & Compliance

- **GDPR Compliance**: Built-in data retention and privacy controls
- **API Rate Limiting**: Configurable delays between API calls
- **Data Validation**: Comprehensive input validation
- **Error Handling**: Graceful error handling and logging
- **Authentication**: Ready for API key or OAuth integration

## 🔍 Monitoring & Analytics

- **Health Check**: `/health` endpoint for system status
- **Analytics**: `/api/v1/analytics/summary` for lead statistics
- **Logging**: Comprehensive logging throughout the system
- **Error Tracking**: Detailed error reporting and handling

## 🚀 Deployment

### Docker Deployment

```dockerfile
FROM python:3.10-slim

WORKDIR /app
COPY . .

RUN pip install -e .

EXPOSE 8000

CMD ["python", "-m", "uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Production Considerations

1. **Database**: Use managed PostgreSQL (Neon, Supabase, etc.)
2. **Caching**: Implement Redis for session management
3. **Load Balancing**: Use nginx or similar for load balancing
4. **Monitoring**: Implement APM tools (DataDog, New Relic)
5. **Security**: Add authentication and rate limiting
6. **Backup**: Regular database backups

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the API documentation at `/docs`
- Review the logs for detailed error information

## 🔮 Roadmap

- [ ] Multi-language support
- [ ] Advanced analytics dashboard
- [ ] Email integration for lead nurturing
- [ ] CRM integration (Salesforce, HubSpot)
- [ ] Machine learning for improved qualification
- [ ] Real-time notifications
- [ ] Mobile application
- [ ] Advanced filtering and search
- [ ] Bulk operations
- [ ] Export functionality (CSV, Excel)

---

**Built with ❤️ using LangGraph, Gemini LLM, and FastAPI**
