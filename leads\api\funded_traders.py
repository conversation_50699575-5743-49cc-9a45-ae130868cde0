"""API endpoints for funded traders service."""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from services.funded_traders_service import FundedTradersService
from database import get_db_session, LeadRepository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/funded-traders", tags=["funded-traders"])

# Initialize service
funded_traders_service = FundedTradersService()


class FundedTraderSearchRequest(BaseModel):
    """Request model for funded trader search."""
    max_results: int = Field(default=100, ge=1, le=500, description="Maximum number of results")
    search_depth: str = Field(default="advanced", description="Search depth (basic, advanced)")
    include_qualification: bool = Field(default=True, description="Include qualification results")
    batch_size: int = Field(default=5, ge=1, le=20, description="Batch size for qualification")


class FundedTraderQualificationRequest(BaseModel):
    """Request model for funded trader qualification."""
    trader_data: Dict[str, Any] = Field(..., description="Trader data to qualify")


class FundedTraderResponse(BaseModel):
    """Response model for funded trader data."""
    trader_name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    platform: Optional[str] = None
    funding_amount: Optional[str] = None
    social_profiles: List[str] = []
    location: Optional[str] = None
    performance: Optional[str] = None
    source_url: Optional[str] = None
    content_snippet: Optional[str] = None
    lead_type: str = "funded_trader"


class FundedTraderQualificationResponse(BaseModel):
    """Response model for funded trader qualification."""
    trader_name: str
    platform: Optional[str] = None
    funding_amount: Optional[str] = None
    score: float
    reasoning: str
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]
    confidence: float
    key_factors: List[str]
    contact_quality: str
    platform_reputation: str
    funding_level: str
    is_qualified: bool
    qualification_threshold: float


class FundedTraderSearchResponse(BaseModel):
    """Response model for funded trader search."""
    traders: List[FundedTraderResponse]
    total_found: int
    search_queries_used: List[str]
    search_summary: Dict[str, Any]


class FundedTraderQualificationSummaryResponse(BaseModel):
    """Response model for funded trader qualification summary."""
    total: int
    qualified: int
    qualified_percentage: float
    average_score: float
    platform_distribution: Dict[str, int]
    funding_level_distribution: Dict[str, int]


@router.post("/search", response_model=FundedTraderSearchResponse)
async def search_funded_traders(
    request: FundedTraderSearchRequest,
    background_tasks: BackgroundTasks
):
    """
    Search for funded traders across multiple platforms.
    
    This endpoint searches for funded traders on:
    - Reddit (r/Forex, r/Trading, etc.)
    - Facebook groups
    - Instagram profiles
    - General web searches
    - Specific prop firm searches (FTMO, MyForexFunds, etc.)
    - Foxex platform searches
    """
    try:
        logger.info(f"Starting funded trader search with max_results={request.max_results}")
        
        # Search for funded traders
        traders = await funded_traders_service.search_funded_traders(
            max_results=request.max_results,
            search_depth=request.search_depth
        )
        
        # Convert to response model
        trader_responses = []
        for trader in traders:
            trader_responses.append(FundedTraderResponse(**trader))
        
        # Get search queries used
        search_queries = funded_traders_service._build_funded_trader_queries()
        
        # Create search summary
        search_summary = {
            "platforms_found": list(set(t.trader_name for t in trader_responses if t.platform)),
            "funding_amounts": list(set(t.funding_amount for t in trader_responses if t.funding_amount)),
            "locations": list(set(t.location for t in trader_responses if t.location)),
            "social_profiles_count": sum(len(t.social_profiles) for t in trader_responses)
        }
        
        # Store results in database if requested
        if request.include_qualification and traders:
            background_tasks.add_task(
                store_funded_traders_in_database, 
                traders, 
                request.batch_size
            )
        
        return FundedTraderSearchResponse(
            traders=trader_responses,
            total_found=len(trader_responses),
            search_queries_used=search_queries,
            search_summary=search_summary
        )
        
    except Exception as e:
        logger.error(f"Error in search_funded_traders: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.post("/qualify", response_model=FundedTraderQualificationResponse)
async def qualify_funded_trader(request: FundedTraderQualificationRequest):
    """
    Qualify a specific funded trader.
    
    This endpoint uses specialized criteria for funded traders:
    - Contact information quality (30%)
    - Trading platform/prop firm reputation (25%)
    - Funding amount (20%)
    - Performance indicators (15%)
    - Professional presence (10%)
    """
    try:
        logger.info(f"Qualifying funded trader: {request.trader_data.get('trader_name', 'Unknown')}")
        
        # Qualify the trader
        qualification_result = await funded_traders_service.qualify_funded_trader(
            request.trader_data
        )
        
        return FundedTraderQualificationResponse(**qualification_result)
        
    except Exception as e:
        logger.error(f"Error in qualify_funded_trader: {e}")
        raise HTTPException(status_code=500, detail=f"Qualification failed: {str(e)}")


@router.post("/qualify-batch", response_model=List[FundedTraderQualificationResponse])
async def qualify_funded_traders_batch(
    traders: List[Dict[str, Any]],
    batch_size: int = 5
):
    """
    Qualify multiple funded traders in batches.
    
    This endpoint processes traders in batches to avoid rate limits
    and provides detailed qualification results for each trader.
    """
    try:
        logger.info(f"Qualifying {len(traders)} funded traders in batches of {batch_size}")
        
        # Qualify traders in batches
        qualification_results = await funded_traders_service.batch_qualify_funded_traders(
            traders, batch_size
        )
        
        # Convert to response models
        responses = []
        for result in qualification_results:
            responses.append(FundedTraderQualificationResponse(**result))
        
        return responses
        
    except Exception as e:
        logger.error(f"Error in qualify_funded_traders_batch: {e}")
        raise HTTPException(status_code=500, detail=f"Batch qualification failed: {str(e)}")


@router.post("/qualify-summary", response_model=FundedTraderQualificationSummaryResponse)
async def get_funded_trader_qualification_summary(
    qualification_results: List[Dict[str, Any]]
):
    """
    Get summary statistics for funded trader qualification results.
    
    This endpoint provides:
    - Total number of traders qualified
    - Number and percentage of qualified traders
    - Average qualification score
    - Distribution by platform reputation
    - Distribution by funding level
    """
    try:
        logger.info(f"Generating qualification summary for {len(qualification_results)} results")
        
        # Get summary
        summary = funded_traders_service.get_qualification_summary(qualification_results)
        
        return FundedTraderQualificationSummaryResponse(**summary)
        
    except Exception as e:
        logger.error(f"Error in get_funded_trader_qualification_summary: {e}")
        raise HTTPException(status_code=500, detail=f"Summary generation failed: {str(e)}")


@router.get("/search-queries")
async def get_funded_trader_search_queries():
    """
    Get the list of search queries used for funded trader discovery.
    
    This endpoint returns all the specialized search queries used to find funded traders,
    including Reddit, Facebook, Instagram, and specific prop firm searches.
    """
    try:
        queries = funded_traders_service._build_funded_trader_queries()
        
        # Categorize queries
        categorized_queries = {
            "reddit": [q for q in queries if "reddit.com" in q],
            "facebook": [q for q in queries if "facebook.com" in q],
            "instagram": [q for q in queries if "instagram.com" in q],
            "foxex": [q for q in queries if "foxex" in q.lower()],
            "prop_firms": [q for q in queries if any(firm in q.lower() for firm in [
                "ftmo", "myforexfunds", "apex", "topstep", "earn2trade", 
                "fundednext", "the5ers", "surgetrader", "city traders", "traders central"
            ])],
            "general": [q for q in queries if not any(platform in q.lower() for platform in [
                "reddit.com", "facebook.com", "instagram.com", "foxex", "ftmo", 
                "myforexfunds", "apex", "topstep", "earn2trade", "fundednext", 
                "the5ers", "surgetrader", "city traders", "traders central"
            ])]
        }
        
        return {
            "total_queries": len(queries),
            "categorized_queries": categorized_queries,
            "all_queries": queries
        }
        
    except Exception as e:
        logger.error(f"Error in get_funded_trader_search_queries: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get search queries: {str(e)}")


@router.get("/platforms")
async def get_supported_trading_platforms():
    """
    Get the list of supported trading platforms and prop firms.
    
    This endpoint returns all the trading platforms and prop firms
    that the service recognizes and searches for.
    """
    try:
        platforms = [
            "Foxex", "FTMO", "MyForexFunds", "Apex Trader Funding", "Topstep",
            "Earn2Trade", "FundedNext", "The5ers", "SurgeTrader", "City Traders Imperium",
            "Traders Central", "Traders With Edge", "True Forex Funds", "Blue Guardian",
            "Trading Fund", "Traders Global", "Traders Academy"
        ]
        
        return {
            "total_platforms": len(platforms),
            "platforms": platforms,
            "major_platforms": ["Foxex", "FTMO", "MyForexFunds", "Apex Trader Funding", "Topstep"],
            "description": "These are the trading platforms and prop firms that the service searches for and recognizes when extracting funded trader information."
        }
        
    except Exception as e:
        logger.error(f"Error in get_supported_trading_platforms: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get platforms: {str(e)}")


async def store_funded_traders_in_database(
    traders: List[Dict[str, Any]], 
    batch_size: int
):
    """Store funded traders in database for later analysis."""
    try:
        async for session in get_db_session():
            lead_repo = LeadRepository(session)
            
            # Store traders in batches
            for i in range(0, len(traders), batch_size):
                batch = traders[i:i + batch_size]
                
                # Store each trader as a lead
                for trader in batch:
                    lead_data = {
                        "name": trader.get("trader_name", "Unknown"),
                        "email": trader.get("email"),
                        "phone": trader.get("phone"),
                        "company": trader.get("platform"),
                        "industry": "funded_trading",
                        "status": "new",
                        "source": "api_search",
                        "discovered_at": datetime.utcnow(),
                        "qualification_score": 0.0,  # Will be updated during qualification
                        "notes": f"Platform: {trader.get('platform', 'Unknown')}, Funding: {trader.get('funding_amount', 'Unknown')}"
                    }
                    
                    await lead_repo.create_lead(lead_data)
                
                # Add delay between batches
                if i + batch_size < len(traders):
                    await asyncio.sleep(1)
            
            break  # Only process one session
        
        logger.info(f"Stored {len(traders)} funded traders in database")
        
    except Exception as e:
        logger.error(f"Error storing funded traders in database: {e}")


# Import datetime for database operations
from datetime import datetime 