"""Test script for the funded traders service."""

import asyncio
import logging
from services.funded_traders_service import FundedTradersService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_funded_traders_service():
    """Test the funded traders service functionality."""
    
    print("🚀 Testing Funded Traders Service")
    print("=" * 50)
    
    # Initialize service
    service = FundedTradersService()
    
    # Test 1: Get search queries
    print("\n📋 Test 1: Getting search queries")
    queries = service._build_funded_trader_queries()
    print(f"Total queries: {len(queries)}")
    
    # Categorize queries
    reddit_queries = [q for q in queries if "reddit.com" in q]
    facebook_queries = [q for q in queries if "facebook.com" in q]
    instagram_queries = [q for q in queries if "instagram.com" in q]
    foxex_queries = [q for q in queries if "foxex" in q.lower()]
    prop_firm_queries = [q for q in queries if any(firm in q.lower() for firm in [
        "ftmo", "myforexfunds", "apex", "topstep", "earn2trade", 
        "fundednext", "the5ers", "surgetrader", "city traders", "traders central"
    ])]
    
    print(f"Reddit queries: {len(reddit_queries)}")
    print(f"Facebook queries: {len(facebook_queries)}")
    print(f"Instagram queries: {len(instagram_queries)}")
    print(f"Foxex queries: {len(foxex_queries)}")
    print(f"Prop firm queries: {len(prop_firm_queries)}")
    
    # Test 2: Search for funded traders (limited results for testing)
    print("\n🔍 Test 2: Searching for funded traders")
    try:
        traders = await service.search_funded_traders(max_results=10, search_depth="basic")
        print(f"Found {len(traders)} traders")
        
        if traders:
            print("\nSample traders found:")
            for i, trader in enumerate(traders[:3], 1):
                print(f"\nTrader {i}:")
                print(f"  Name: {trader.get('trader_name', 'Unknown')}")
                print(f"  Platform: {trader.get('platform', 'Unknown')}")
                print(f"  Funding: {trader.get('funding_amount', 'Unknown')}")
                print(f"  Email: {trader.get('email', 'Unknown')}")
                print(f"  Phone: {trader.get('phone', 'Unknown')}")
                print(f"  Location: {trader.get('location', 'Unknown')}")
                print(f"  Performance: {trader.get('performance', 'Unknown')}")
                print(f"  Social Profiles: {trader.get('social_profiles', [])}")
        
    except Exception as e:
        print(f"Error in search: {e}")
    
    # Test 3: Qualify a sample trader
    print("\n✅ Test 3: Qualifying a sample trader")
    sample_trader = {
        "trader_name": "John Smith",
        "platform": "FTMO",
        "funding_amount": "$100k",
        "email": "<EMAIL>",
        "phone": "******-123-4567",
        "location": "New York",
        "performance": "15%",
        "social_profiles": ["@johnsmith", "instagram.com/johnsmith"],
        "source_url": "https://example.com/trader",
        "content_snippet": "John Smith is a successful funded trader on FTMO with $100k funding and 15% profit."
    }
    
    try:
        qualification = await service.qualify_funded_trader(sample_trader)
        print(f"Qualification Score: {qualification.get('score', 0):.2f}")
        print(f"Qualified: {qualification.get('is_qualified', False)}")
        print(f"Reasoning: {qualification.get('reasoning', 'No reasoning')}")
        print(f"Strengths: {qualification.get('strengths', [])}")
        print(f"Weaknesses: {qualification.get('weaknesses', [])}")
        print(f"Contact Quality: {qualification.get('contact_quality', 'Unknown')}")
        print(f"Platform Reputation: {qualification.get('platform_reputation', 'Unknown')}")
        print(f"Funding Level: {qualification.get('funding_level', 'Unknown')}")
        
    except Exception as e:
        print(f"Error in qualification: {e}")
    
    # Test 4: Batch qualification
    print("\n📊 Test 4: Batch qualification")
    sample_traders = [
        {
            "trader_name": "Alice Johnson",
            "platform": "MyForexFunds",
            "funding_amount": "$50k",
            "email": "<EMAIL>",
            "phone": "******-987-6543",
            "location": "Los Angeles",
            "performance": "12%",
            "social_profiles": ["@alicejohnson"],
            "source_url": "https://example.com/alice",
            "content_snippet": "Alice Johnson is a funded trader on MyForexFunds with $50k funding."
        },
        {
            "trader_name": "Bob Wilson",
            "platform": "Foxex",
            "funding_amount": "$200k",
            "email": "<EMAIL>",
            "phone": "******-456-7890",
            "location": "Chicago",
            "performance": "18%",
            "social_profiles": ["@bobwilson", "linkedin.com/in/bobwilson"],
            "source_url": "https://example.com/bob",
            "content_snippet": "Bob Wilson is a successful trader on Foxex with $200k funding and 18% profit."
        }
    ]
    
    try:
        batch_results = await service.batch_qualify_funded_traders(sample_traders, batch_size=2)
        print(f"Batch qualified {len(batch_results)} traders")
        
        for i, result in enumerate(batch_results, 1):
            print(f"\nTrader {i} - {result.get('trader_name', 'Unknown')}:")
            print(f"  Score: {result.get('score', 0):.2f}")
            print(f"  Qualified: {result.get('is_qualified', False)}")
            print(f"  Platform: {result.get('platform_reputation', 'Unknown')}")
            print(f"  Funding: {result.get('funding_level', 'Unknown')}")
        
        # Get summary
        summary = service.get_qualification_summary(batch_results)
        print(f"\nSummary:")
        print(f"  Total: {summary['total']}")
        print(f"  Qualified: {summary['qualified']}")
        print(f"  Qualified %: {summary['qualified_percentage']:.1f}%")
        print(f"  Average Score: {summary['average_score']:.2f}")
        
    except Exception as e:
        print(f"Error in batch qualification: {e}")
    
    # Test 5: Test supported platforms
    print("\n🏢 Test 5: Supported platforms")
    platforms = [
        "Foxex", "FTMO", "MyForexFunds", "Apex Trader Funding", "Topstep",
        "Earn2Trade", "FundedNext", "The5ers", "SurgeTrader", "City Traders Imperium",
        "Traders Central", "Traders With Edge", "True Forex Funds", "Blue Guardian",
        "Trading Fund", "Traders Global", "Traders Academy"
    ]
    
    print(f"Supported platforms: {len(platforms)}")
    print("Major platforms:", platforms[:5])
    
    print("\n✅ Funded Traders Service Test Completed!")


async def test_api_endpoints():
    """Test the API endpoints for funded traders."""
    
    print("\n🌐 Testing Funded Traders API Endpoints")
    print("=" * 50)
    
    import httpx
    
    # Base URL for the API
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        try:
            # Test 1: Get search queries
            print("\n📋 Test 1: GET /funded-traders/search-queries")
            response = await client.get(f"{base_url}/funded-traders/search-queries")
            if response.status_code == 200:
                data = response.json()
                print(f"Total queries: {data.get('total_queries', 0)}")
                print(f"Reddit queries: {len(data.get('categorized_queries', {}).get('reddit', []))}")
                print(f"Facebook queries: {len(data.get('categorized_queries', {}).get('facebook', []))}")
                print(f"Instagram queries: {len(data.get('categorized_queries', {}).get('instagram', []))}")
                print(f"Foxex queries: {len(data.get('categorized_queries', {}).get('foxex', []))}")
            else:
                print(f"Error: {response.status_code}")
            
            # Test 2: Get supported platforms
            print("\n🏢 Test 2: GET /funded-traders/platforms")
            response = await client.get(f"{base_url}/funded-traders/platforms")
            if response.status_code == 200:
                data = response.json()
                print(f"Total platforms: {data.get('total_platforms', 0)}")
                print(f"Major platforms: {data.get('major_platforms', [])}")
            else:
                print(f"Error: {response.status_code}")
            
            # Test 3: Search for funded traders
            print("\n🔍 Test 3: POST /funded-traders/search")
            search_request = {
                "max_results": 5,
                "search_depth": "basic",
                "include_qualification": False,
                "batch_size": 2
            }
            
            response = await client.post(f"{base_url}/funded-traders/search", json=search_request)
            if response.status_code == 200:
                data = response.json()
                print(f"Found {data.get('total_found', 0)} traders")
                print(f"Search queries used: {len(data.get('search_queries_used', []))}")
                
                if data.get('traders'):
                    print("\nSample traders:")
                    for trader in data['traders'][:2]:
                        print(f"  - {trader.get('trader_name', 'Unknown')} ({trader.get('platform', 'Unknown')})")
            else:
                print(f"Error: {response.status_code}")
                print(f"Response: {response.text}")
            
            # Test 4: Qualify a trader
            print("\n✅ Test 4: POST /funded-traders/qualify")
            trader_data = {
                "trader_name": "Test Trader",
                "platform": "FTMO",
                "funding_amount": "$100k",
                "email": "<EMAIL>",
                "phone": "******-123-4567",
                "location": "New York",
                "performance": "15%",
                "social_profiles": ["@testtrader"],
                "source_url": "https://example.com/test",
                "content_snippet": "Test Trader is a successful funded trader on FTMO."
            }
            
            response = await client.post(f"{base_url}/funded-traders/qualify", json={"trader_data": trader_data})
            if response.status_code == 200:
                data = response.json()
                print(f"Qualification Score: {data.get('score', 0):.2f}")
                print(f"Qualified: {data.get('is_qualified', False)}")
                print(f"Contact Quality: {data.get('contact_quality', 'Unknown')}")
                print(f"Platform Reputation: {data.get('platform_reputation', 'Unknown')}")
            else:
                print(f"Error: {response.status_code}")
                print(f"Response: {response.text}")
            
        except Exception as e:
            print(f"Error testing API endpoints: {e}")
    
    print("\n✅ API Endpoints Test Completed!")


async def main():
    """Main test function."""
    print("🧪 Funded Traders Service & API Test Suite")
    print("=" * 60)
    
    # Test the service directly
    await test_funded_traders_service()
    
    # Test the API endpoints (if server is running)
    print("\n" + "=" * 60)
    print("Note: API tests require the server to be running on localhost:8000")
    print("Run 'python -m uvicorn api.main:app --reload' to start the server")
    
    # Uncomment the line below to test API endpoints when server is running
    # await test_api_endpoints()


if __name__ == "__main__":
    asyncio.run(main()) 